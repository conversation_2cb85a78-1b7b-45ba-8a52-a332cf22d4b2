%%{
/*
 * Copyright (c) 2018 Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/*
 *  ======== DAC12.Board.h.xdt ========
 */

    /* args[] passed by /ti/driverlib/templates/Board.h.xdt */
    let DAC12 = args[0];
    let content = args[1];

    /* get ti/drivers common utility functions */
    let Common = system.getScript("/ti/driverlib/Common.js");

    let inst = DAC12.$static;

    switch(content) {
        case "Define":
            printDefine();
            break;
        case "Declare":
            printDeclare();
            break;
        default:
            /* do nothing */
            return;
    }
%%}
%
% function printDefine() {
%       let flavor = inst.peripheral.$solution.peripheralName;
%       let nameStr = "#define "+ "DAC12";

/* Defines for DAC12 */
`(nameStr+"_IRQHandler").padEnd(40," ") + (flavor+"_IRQHandler").padStart(40," ")`
`(nameStr+"_INT_IRQN").padEnd(40," ") + ("("+flavor+"_INT_IRQn)").padStart(40," ")`
%       let gpioStr = "GPIO_"+"DAC12";
%       if (inst.dacOutputPinEn){
%       let outPinName = "Out"+"Pin";
%       let outPackagePin = inst.peripheral[outPinName].$solution.packagePinName;
%       let outPinCM = Common.getPinCM(outPackagePin,inst,"OUT");
%       let outGpioName = system.deviceData.devicePins[outPackagePin].mux.muxSetting[0].peripheralPin.peripheralName;
%       let outPort = Common.getGPIOPortMultiPad(outPackagePin,inst,"OUT");
%       let outGpioPin = Common.getGPIONumberMultiPad(outPackagePin,inst,"OUT");
%       let outPortStr = "#define "+gpioStr+"_OUT_PORT";
`outPortStr.padEnd(50," ")+outPort.padStart(30, " ")`
% //#define GPIO_x_OUT_PORT
%       let outPinStr = "#define "+gpioStr+"_OUT_PIN";
%       let outPinStr2 = "DL_GPIO_PIN_"+outGpioPin;
`outPinStr.padEnd(50," ")+outPinStr2.padStart(30," ")`
% //#define GPIO_x_OUT_PIN
%       let outIOmuxStr = "#define "+gpioStr+"_IOMUX_OUT";
%       let outIOmuxStr2 = "(IOMUX_PINCM"+outPinCM.toString()+")";
`outIOmuxStr.padEnd(50," ")+outIOmuxStr2.padStart(30," ")`
% //#define GPIO_x_IOMUX_OUT
%       let outFuncStr = "#define "+gpioStr+"_IOMUX_OUT_FUNC";
%       let outFuncStr2 = "IOMUX_PINCM"+outPinCM+"_PF_UNCONNECTED";
`outFuncStr.padEnd(50, " ")+outFuncStr2.padStart(30, " ")`
% //#define GPIO_x_IOMUX_OUT_FUNC
%   }
% } // function printDefine
%
% function printDeclare() {
void SYSCFG_DL_DAC12_init(void);
% } // function printDeclare
