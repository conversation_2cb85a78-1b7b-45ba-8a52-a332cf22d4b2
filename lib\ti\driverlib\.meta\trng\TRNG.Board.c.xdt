%%{
/*
 * Copyright (c) 2018-2019, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CO<PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/*
 *  ======== TRNG.Board.c.xdt ========
 */
    /* args[] passed by /ti/drivers/templates/Board.c.xdt during function call: */
    let TRNG = args[0];
    let content = args[1];

    /* get Common /ti/driverlib utility functions */
    let Common = system.getScript("/ti/driverlib/Common.js");

    /* shorthand names for some common references in template below */
    let stat = TRNG.$static;
    if (stat.length == 0) return;

    /*
     * Standard Trampoline:
     * In order to preserve spacing, it is important to also set the boolean
     * values in the templates object based on whether that condition should
     * produce any code
     * Example:
     * templates: {
     *       boardc: "/ti/driverlib/comp/COMP.Board.c.xdt",
     *       boardh: "/ti/driverlib/comp/COMP.Board.h.xdt",
     *       Call: true,
     *       Reset: false,
     *       Power: true,
     *       GPIO: false,
     *       Function: true
     * },
     */

    switch(content) {
        case "Call":
            printCall();
            break;
        case "Reset":
            printReset();
            break;
        case "Power":
            printPower();
            break;
        case "GPIO":
            printGPIO();
            break;
        case "Function":
            printFunction();
            break;
        case "RetentionDeclare":
            printRetentionDeclare();
            break;
        case "RetentionSave":
            printRetentionSave();
            break;
        case "RetentionRestore":
            printRetentionRestore();
            break;
        case "RetentionRdy":
            printRetentionRdy();
            break;
        default:
            /* do nothing */
            break;
    }
%%}
%
% function printCall() {
    SYSCFG_DL_TRNG_init();
% }
%
% /* Retention Configuration Code */
% function printRetentionDeclare(){
`Common.getStaticRetentionDeclareC(stat,"TRNG")`
% }
% function printRetentionRdy(){
`Common.getStaticRetentionRdyC(stat,"TRNG")`
% }
% function printRetentionSave(){
`Common.getStaticRetentionSaveC(stat,"TRNG")`
% }
% function printRetentionRestore(){
`Common.getStaticRetentionRestoreC(stat,"TRNG")`
% }
%
% function printReset() {
    DL_TRNG_reset(TRNG);
%
% }
%
% function printPower() {
    DL_TRNG_enablePower(TRNG);
% }
%
% function printGPIO() {
% /* Not applicable to TRNG */
% }
%
% /* Main Function */
% function printFunction() {
SYSCONFIG_WEAK void SYSCFG_DL_TRNG_init(void)
{
    DL_TRNG_setClockDivider(TRNG, `stat.clockDivider`);

%   if (stat.executeDigitalBlockSelfTest) {
    `createDigitalBlockSelfTestCode()`;

%   }
%
%   if (stat.executeAnalogBlockSelfTest) {
    `createAnalogBlockSelfTestCode()`;

%   }
%
%   if (stat.executeDigitalBlockSelfTest || stat.executeAnalogBlockSelfTest) {
    /*
     * TRNG doesn't automatically transition to NORM_FUNC after health tests.
     * This is a mismatch with the TRM
     */
%   }
%   // Must enter NORM func state, the device is brought up in the OFF State
    DL_TRNG_sendCommand(TRNG, DL_TRNG_CMD_NORM_FUNC);
    while (!DL_TRNG_isCommandDone(TRNG))
        ;
    DL_TRNG_clearInterruptStatus(TRNG, DL_TRNG_INTERRUPT_CMD_DONE_EVENT);

%
%   /* Decimation by 1 means no decimation */
%   if (stat.decimationRate != "DL_TRNG_DECIMATION_RATE_1") {
    DL_TRNG_setDecimationRate(TRNG, `stat.decimationRate`);
%   }
%
%   if (stat.enabledInterrupts.length > 0) {
%   let intsToEnableOR = createInterruptBitwiseOR();

    DL_TRNG_clearInterruptStatus(TRNG, `intsToEnableOR`);

    DL_TRNG_enableInterrupt(TRNG, `intsToEnableOR`);
%   }
}
% }
%
% function createDigitalBlockSelfTestCode() {
%%{
    let digitalTestCodeGen = ``;

    digitalTestCodeGen += `
    /* Run digital block start-up self-test */
    DL_TRNG_sendCommand(TRNG, DL_TRNG_CMD_TEST_DIG);
    while (!DL_TRNG_isCommandDone(TRNG))
        ;
    DL_TRNG_clearInterruptStatus(TRNG, DL_TRNG_INTERRUPT_CMD_DONE_EVENT);

    /* Must delay for ~100000 cycles before reading TEST_RESULTS */
    delay_cycles(100000);

    if (DL_TRNG_getDigitalHealthTestResults(TRNG) !=
        DL_TRNG_DIGITAL_HEALTH_TEST_SUCCESS)
    {
        __BKPT(0);
    }`;

    return (digitalTestCodeGen);
%%}
% }
%
% function createAnalogBlockSelfTestCode() {
%%{
    let analogTestCodeGen = ``;

    analogTestCodeGen += `
    /* Run analog block start-up self-test */
    DL_TRNG_sendCommand(TRNG, DL_TRNG_CMD_TEST_ANA);
    while (!DL_TRNG_isCommandDone(TRNG))
        ;
    DL_TRNG_clearInterruptStatus(TRNG, DL_TRNG_INTERRUPT_CMD_DONE_EVENT);

    /* Must delay for ~100000 cycles before reading TEST_RESULTS */
    delay_cycles(100000);

    if (DL_TRNG_getAnalogHealthTestResults(TRNG) !=
        DL_TRNG_ANALOG_HEALTH_TEST_SUCCESS)
    {
        __BKPT(0);
    }`;

    return (analogTestCodeGen);
%%}
% }
%
% function createInterruptBitwiseOR() {
%%{
    let intsToEnableOR = "(";

    for (let intToEnable of stat.enabledInterrupts)
    {
        /* The final item should end with a parenthesis */
        if (intToEnable == stat.enabledInterrupts[stat.enabledInterrupts.length - 1])
        {
            intsToEnableOR += (intToEnable + ")");
        }
        else
        {
            intsToEnableOR += (intToEnable + " |");

            intsToEnableOR += "\n        ";
        }
    }

    return (intsToEnableOR);
%%}
% }
