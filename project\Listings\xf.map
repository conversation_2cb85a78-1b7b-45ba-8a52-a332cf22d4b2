Component: Arm Compiler for Embedded 6.19 Tool: armlink [5e73cb00]

==============================================================================

Section Cross References

    dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig) refers to dl_adc12.o(.text.DL_ADC12_setClockConfig) for [Anonymous Symbol]
    dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig) refers to dl_adc12.o(.text.DL_ADC12_getClockConfig) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_setKey) refers to dl_aes.o(.text.DL_AES_setKey) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_setKeyAligned) refers to dl_aes.o(.text.DL_AES_setKeyAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataIn) refers to dl_aes.o(.text.DL_AES_loadDataIn) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadDataInAligned) refers to dl_aes.o(.text.DL_AES_loadDataInAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_getDataOut) refers to dl_aes.o(.text.DL_AES_getDataOut) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_getDataOutAligned) refers to dl_aes.o(.text.DL_AES_getDataOutAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataIn) refers to dl_aes.o(.text.DL_AES_loadXORDataIn) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInAligned) refers to dl_aes.o(.text.DL_AES_loadXORDataInAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTrigger) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTriggerAligned) refers to dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_xorData) refers to dl_aes.o(.text.DL_AES_xorData) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_xorDataAligned) refers to dl_aes.o(.text.DL_AES_xorDataAligned) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_saveConfiguration) refers to dl_aes.o(.text.DL_AES_saveConfiguration) for [Anonymous Symbol]
    dl_aes.o(.ARM.exidx.text.DL_AES_restoreConfiguration) refers to dl_aes.o(.text.DL_AES_restoreConfiguration) for [Anonymous Symbol]
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock32) refers to dl_crc.o(.text.DL_CRC_calculateBlock32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange32) refers to dl_crc.o(.text.DL_CRC_calculateMemoryRange32) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock16) refers to dl_crc.o(.text.DL_CRC_calculateBlock16) for [Anonymous Symbol]
    dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange16) refers to dl_crc.o(.text.DL_CRC_calculateMemoryRange16) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_init) refers to dl_dac12.o(.text.DL_DAC12_init) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking8) refers to dl_dac12.o(.text.DL_DAC12_outputBlocking8) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking12) refers to dl_dac12.o(.text.DL_DAC12_outputBlocking12) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO8) refers to dl_dac12.o(.text.DL_DAC12_fillFIFO8) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO12) refers to dl_dac12.o(.text.DL_DAC12_fillFIFO12) for [Anonymous Symbol]
    dl_dac12.o(.ARM.exidx.text.DL_DAC12_performSelfCalibrationBlocking) refers to dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking) for [Anonymous Symbol]
    dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel) refers to dl_dma.o(.text.DL_DMA_initChannel) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.ramfunc) refers to dl_flashctl.o(.ramfunc) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for DL_FlashCTL_eraseDataBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massErase) refers to dl_flashctl.o(.text.DL_FlashCTL_massErase) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectNonMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) for DL_FlashCTL_eraseDataBankFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBankFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for DL_FlashCTL_eraseDataBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank) for DL_FlashCTL_eraseDataBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryReset) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryReset) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectNonMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM) for DL_FlashCTL_eraseDataBankFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank) for DL_FlashCTL_massEraseMultiBank
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetMultiBank) refers to dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectSector) for DL_FlashCTL_unprotectSector
    dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectDataMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectMainMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectMainMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectAllMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_unprotectAllMemory) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectAllMemory) refers to dl_flashctl.o(.text.DL_FlashCTL_protectAllMemory) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_protectSector) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectSector) refers to dl_flashctl.o(.text.DL_FlashCTL_protectSector) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCGenerated) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCManual) refers to dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual) for [Anonymous Symbol]
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerify) refers to dl_flashctl.o(.text.DL_FlashCTL_blankVerify) for [Anonymous Symbol]
    dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.ramfunc) for DL_FlashCTL_executeCommandFromRAM
    dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerifyFromRAM) refers to dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_mathacl.o(.ARM.exidx.text.DL_MathACL_configOperation) refers to dl_mathacl.o(.text.DL_MathACL_configOperation) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady) refers to dl_mcan.o(.text.DL_MCAN_isReady) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig) refers to dl_mcan.o(.text.DL_MCAN_setClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig) refers to dl_mcan.o(.text.DL_MCAN_getClockConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset) refers to dl_mcan.o(.text.DL_MCAN_isInReset) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable) refers to dl_mcan.o(.text.DL_MCAN_isFDOpEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone) refers to dl_mcan.o(.text.DL_MCAN_isMemInitDone) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode) refers to dl_mcan.o(.text.DL_MCAN_setOpMode) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode) refers to dl_mcan.o(.text.DL_MCAN_getOpMode) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_init) refers to dl_mcan.o(.text.DL_MCAN_init) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_config) refers to dl_mcan.o(.text.DL_MCAN_config) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig) refers to dl_mcan.o(.text.DL_MCAN_eccConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime) refers to dl_mcan.o(.text.DL_MCAN_setBitTime) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig) refers to dl_mcan.o(.text.DL_MCAN_msgRAMConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask) refers to dl_mcan.o(.text.DL_MCAN_setExtIDAndMask) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.rodata.cst32) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam) refers to dl_mcan.o(.text.DL_MCAN_writeMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq) refers to dl_mcan.o(.text.DL_MCAN_TXBufAddReq) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_getNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus) refers to dl_mcan.o(.text.DL_MCAN_clearNewDataStatus) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.rodata.cst32) for [Anonymous Symbol]
    dl_mcan.o(.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam) refers to dl_mcan.o(.text.DL_MCAN_readMsgRam) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO) refers to dl_mcan.o(.text.DL_MCAN_readTxEventFIFO) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter) refers to dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable) refers to dl_mcan.o(.text.DL_MCAN_lpbkModeEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters) refers to dl_mcan.o(.text.DL_MCAN_getErrCounters) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus) refers to dl_mcan.o(.text.DL_MCAN_getProtocolStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr) refers to dl_mcan.o(.text.DL_MCAN_enableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine) refers to dl_mcan.o(.text.DL_MCAN_selectIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine) refers to dl_mcan.o(.text.DL_MCAN_enableIntrLine) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_getIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_clearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus) refers to dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend) refers to dl_mcan.o(.text.DL_MCAN_getTxBufReqPend) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationReq) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus) refers to dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus) refers to dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest) refers to dl_mcan.o(.text.DL_MCAN_addClockStopRequest) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck) refers to dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError) refers to dl_mcan.o(.text.DL_MCAN_eccForceError) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_eccWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_eccEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus) refers to dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterConfig) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSCounterEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr) refers to dl_mcan.o(.text.DL_MCAN_extTSEnableIntr) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI) refers to dl_mcan.o(.text.DL_MCAN_extTSWriteEOI) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount) refers to dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId) refers to dl_mcan.o(.text.DL_MCAN_getRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClockStopAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus) refers to dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState) refers to dl_mcan.o(.text.DL_MCAN_getRxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState) refers to dl_mcan.o(.text.DL_MCAN_setTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState) refers to dl_mcan.o(.text.DL_MCAN_getTxPinState) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTSCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck) refers to dl_mcan.o(.text.DL_MCAN_getClkStopAck) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime) refers to dl_mcan.o(.text.DL_MCAN_getBitTime) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter) refers to dl_mcan.o(.text.DL_MCAN_resetTSCounter) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal) refers to dl_mcan.o(.text.DL_MCAN_getTOCounterVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId) refers to dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable) refers to dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal) refers to dl_mcan.o(.text.DL_MCAN_getEndianVal) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask) refers to dl_mcan.o(.text.DL_MCAN_getExtIDANDMask) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration) refers to dl_mcan.o(.text.DL_MCAN_saveConfiguration) for [Anonymous Symbol]
    dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration) refers to dl_mcan.o(.text.DL_MCAN_restoreConfiguration) for [Anonymous Symbol]
    dl_opa.o(.ARM.exidx.text.DL_OPA_increaseGain) refers to dl_opa.o(.text.DL_OPA_increaseGain) for [Anonymous Symbol]
    dl_opa.o(.ARM.exidx.text.DL_OPA_decreaseGain) refers to dl_opa.o(.text.DL_OPA_decreaseGain) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_initCalendar) refers to dl_rtc_common.o(.text.DL_RTC_Common_initCalendar) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarTime) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm1) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2) for [Anonymous Symbol]
    dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm2) refers to dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_init) refers to dl_spi.o(.text.DL_SPI_init) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig) refers to dl_spi.o(.text.DL_SPI_getClockConfig) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32) refers to dl_spi.o(.text.DL_SPI_receiveDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32) refers to dl_spi.o(.text.DL_SPI_transmitDataBlocking32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32) refers to dl_spi.o(.text.DL_SPI_receiveDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32) refers to dl_spi.o(.text.DL_SPI_transmitDataCheck32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32) refers to dl_spi.o(.text.DL_SPI_drainRXFIFO32) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO8) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO16) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for [Anonymous Symbol]
    dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32) refers to dl_spi.o(.text.DL_SPI_fillTXFIFO32) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initPWMMode) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_initPWMMode) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_trng.o(.ARM.exidx.text.DL_TRNG_saveConfiguration) refers to dl_trng.o(.text.DL_TRNG_saveConfiguration) for [Anonymous Symbol]
    dl_trng.o(.ARM.exidx.text.DL_TRNG_restoreConfiguration) refers to dl_trng.o(.text.DL_TRNG_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_configReference) refers to dl_vref.o(.text.DL_VREF_configReference) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_setClockConfig) refers to dl_vref.o(.text.DL_VREF_setClockConfig) for [Anonymous Symbol]
    dl_vref.o(.ARM.exidx.text.DL_VREF_getClockConfig) refers to dl_vref.o(.text.DL_VREF_getClockConfig) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_registerInterrupt) refers to dl_interrupt.o(.vtable) for [Anonymous Symbol]
    dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_registerInterrupt) refers to dl_interrupt.o(.text.DL_Interrupt_registerInterrupt) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) refers to dl_interrupt.o(.vtable) for [Anonymous Symbol]
    dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) refers to startup_mspm0g350x_uvision.o(.text) for Default_Handler
    dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_unregisterInterrupt) refers to dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY) for [Anonymous Symbol]
    main.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    main.o(.text.main) refers to maixcam.o(.text.MaixCam_Init) for MaixCam_Init
    main.o(.text.main) refers to oled.o(.text.OLED_Init) for OLED_Init
    main.o(.text.main) refers to enconder.o(.text.Encoder_init) for Encoder_init
    main.o(.text.main) refers to emm_v5.o(.text.Emm_V5_En_Control) for Emm_V5_En_Control
    main.o(.text.main) refers to delay.o(.text.delay_noOS) for delay_noOS
    main.o(.text.main) refers to startuptask.o(.text.StartupTask) for StartupTask
    main.o(.text.main) refers to tasks.o(.text.vTaskStartScheduler) for vTaskStartScheduler
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) for SYSCFG_DL_MOTOR_PWM_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init) for SYSCFG_DL_Servo_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init) for SYSCFG_DL_QEI_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for SYSCFG_DL_TIMER_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for SYSCFG_DL_UART_1_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) for SYSCFG_DL_UART_3_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init) for SYSCFG_DL_SPI_WS2812_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gQEI_0Backup) for gQEI_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gServoBackup) for gServoBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gTIMER_0Backup) for gTIMER_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_initPWMMode) for DL_Timer_initPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to ti_msp_dl_config.o(.rodata.gMOTOR_PWMClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) refers to ti_msp_dl_config.o(.rodata.gMOTOR_PWMConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MOTOR_PWM_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init) refers to dl_timer.o(.text.DL_TimerA_initPWMMode) for DL_TimerA_initPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init) refers to ti_msp_dl_config.o(.rodata.gServoClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init) refers to ti_msp_dl_config.o(.rodata.gServoConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_Servo_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init) refers to ti_msp_dl_config.o(.rodata.gQEI_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_QEI_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for DL_Timer_initTimerMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) refers to ti_msp_dl_config.o(.rodata.gUART_3ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) refers to ti_msp_dl_config.o(.rodata.gUART_3Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_3_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init) refers to dl_spi.o(.text.DL_SPI_setClockConfig) for DL_SPI_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init) refers to dl_spi.o(.text.DL_SPI_init) for DL_SPI_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init) refers to ti_msp_dl_config.o(.rodata.gSPI_WS2812_clockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init) refers to ti_msp_dl_config.o(.rodata.gSPI_WS2812_config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SPI_WS2812_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for DL_TimerA_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for DL_Timer_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for DL_UART_Main_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_spi.o(.text.DL_SPI_saveConfiguration) for DL_SPI_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gServoBackup) for gServoBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gQEI_0Backup) for gQEI_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gTIMER_0Backup) for gTIMER_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for DL_TimerA_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for DL_Timer_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for DL_UART_Main_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_spi.o(.text.DL_SPI_restoreConfiguration) for DL_SPI_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gServoBackup) for gServoBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gQEI_0Backup) for gQEI_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gTIMER_0Backup) for gTIMER_0Backup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to port.o(.text.SVC_Handler) for SVC_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to port.o(.text.PendSV_Handler) for PendSV_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to port.o(.text.SysTick_Handler) for SysTick_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to maixcam.o(.text.UART3_IRQHandler) for UART3_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    event_groups.o(.text.xEventGroupCreate) refers to heap_4.o(.text.pvPortMalloc) for pvPortMalloc
    event_groups.o(.text.xEventGroupCreate) refers to list.o(.text.vListInitialise) for vListInitialise
    event_groups.o(.ARM.exidx.text.xEventGroupCreate) refers to event_groups.o(.text.xEventGroupCreate) for [Anonymous Symbol]
    event_groups.o(.text.xEventGroupSync) refers to tasks.o(.text.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(.text.xEventGroupSync) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(.text.xEventGroupSync) refers to tasks.o(.text.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(.text.xEventGroupSync) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(.text.xEventGroupSync) refers to tasks.o(.text.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(.text.xEventGroupSync) refers to port.o(.text.vPortYield) for vPortYield
    event_groups.o(.text.xEventGroupSync) refers to tasks.o(.text.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(.text.xEventGroupSync) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(.text.xEventGroupSync) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    event_groups.o(.ARM.exidx.text.xEventGroupSync) refers to event_groups.o(.text.xEventGroupSync) for [Anonymous Symbol]
    event_groups.o(.text.xEventGroupSetBits) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(.text.xEventGroupSetBits) refers to tasks.o(.text.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(.text.xEventGroupSetBits) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(.ARM.exidx.text.xEventGroupSetBits) refers to event_groups.o(.text.xEventGroupSetBits) for [Anonymous Symbol]
    event_groups.o(.text.xEventGroupWaitBits) refers to tasks.o(.text.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(.text.xEventGroupWaitBits) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(.text.xEventGroupWaitBits) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(.text.xEventGroupWaitBits) refers to tasks.o(.text.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(.text.xEventGroupWaitBits) refers to port.o(.text.vPortYield) for vPortYield
    event_groups.o(.text.xEventGroupWaitBits) refers to tasks.o(.text.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(.text.xEventGroupWaitBits) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(.text.xEventGroupWaitBits) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    event_groups.o(.ARM.exidx.text.xEventGroupWaitBits) refers to event_groups.o(.text.xEventGroupWaitBits) for [Anonymous Symbol]
    event_groups.o(.text.xEventGroupClearBits) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(.text.xEventGroupClearBits) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    event_groups.o(.ARM.exidx.text.xEventGroupClearBits) refers to event_groups.o(.text.xEventGroupClearBits) for [Anonymous Symbol]
    event_groups.o(.text.xEventGroupClearBitsFromISR) refers to timers.o(.text.xTimerPendFunctionCallFromISR) for xTimerPendFunctionCallFromISR
    event_groups.o(.text.xEventGroupClearBitsFromISR) refers to event_groups.o(.text.vEventGroupClearBitsCallback) for vEventGroupClearBitsCallback
    event_groups.o(.ARM.exidx.text.xEventGroupClearBitsFromISR) refers to event_groups.o(.text.xEventGroupClearBitsFromISR) for [Anonymous Symbol]
    event_groups.o(.text.vEventGroupClearBitsCallback) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(.text.vEventGroupClearBitsCallback) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    event_groups.o(.ARM.exidx.text.vEventGroupClearBitsCallback) refers to event_groups.o(.text.vEventGroupClearBitsCallback) for [Anonymous Symbol]
    event_groups.o(.text.xEventGroupGetBitsFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    event_groups.o(.text.xEventGroupGetBitsFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    event_groups.o(.ARM.exidx.text.xEventGroupGetBitsFromISR) refers to event_groups.o(.text.xEventGroupGetBitsFromISR) for [Anonymous Symbol]
    event_groups.o(.text.vEventGroupDelete) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(.text.vEventGroupDelete) refers to tasks.o(.text.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(.text.vEventGroupDelete) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(.text.vEventGroupDelete) refers to heap_4.o(.text.vPortFree) for vPortFree
    event_groups.o(.ARM.exidx.text.vEventGroupDelete) refers to event_groups.o(.text.vEventGroupDelete) for [Anonymous Symbol]
    event_groups.o(.text.vEventGroupSetBitsCallback) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(.text.vEventGroupSetBitsCallback) refers to tasks.o(.text.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(.text.vEventGroupSetBitsCallback) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(.ARM.exidx.text.vEventGroupSetBitsCallback) refers to event_groups.o(.text.vEventGroupSetBitsCallback) for [Anonymous Symbol]
    event_groups.o(.text.xEventGroupSetBitsFromISR) refers to timers.o(.text.xTimerPendFunctionCallFromISR) for xTimerPendFunctionCallFromISR
    event_groups.o(.text.xEventGroupSetBitsFromISR) refers to event_groups.o(.text.vEventGroupSetBitsCallback) for vEventGroupSetBitsCallback
    event_groups.o(.ARM.exidx.text.xEventGroupSetBitsFromISR) refers to event_groups.o(.text.xEventGroupSetBitsFromISR) for [Anonymous Symbol]
    event_groups.o(.ARM.exidx.text.uxEventGroupGetNumber) refers to event_groups.o(.text.uxEventGroupGetNumber) for [Anonymous Symbol]
    event_groups.o(.ARM.exidx.text.vEventGroupSetNumber) refers to event_groups.o(.text.vEventGroupSetNumber) for [Anonymous Symbol]
    list.o(.ARM.exidx.text.vListInitialise) refers to list.o(.text.vListInitialise) for [Anonymous Symbol]
    list.o(.ARM.exidx.text.vListInitialiseItem) refers to list.o(.text.vListInitialiseItem) for [Anonymous Symbol]
    list.o(.ARM.exidx.text.vListInsertEnd) refers to list.o(.text.vListInsertEnd) for [Anonymous Symbol]
    list.o(.ARM.exidx.text.vListInsert) refers to list.o(.text.vListInsert) for [Anonymous Symbol]
    list.o(.ARM.exidx.text.uxListRemove) refers to list.o(.text.uxListRemove) for [Anonymous Symbol]
    queue.o(.text.xQueueGenericReset) refers to llmul.o(.text) for __aeabi_lmul
    queue.o(.text.xQueueGenericReset) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.xQueueGenericReset) refers to list.o(.text.vListInitialise) for vListInitialise
    queue.o(.text.xQueueGenericReset) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.text.xQueueGenericReset) refers to tasks.o(.text.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(.text.xQueueGenericReset) refers to port.o(.text.vPortYield) for vPortYield
    queue.o(.ARM.exidx.text.xQueueGenericReset) refers to queue.o(.text.xQueueGenericReset) for [Anonymous Symbol]
    queue.o(.text.xQueueGenericCreate) refers to llmul.o(.text) for __aeabi_lmul
    queue.o(.text.xQueueGenericCreate) refers to heap_4.o(.text.pvPortMalloc) for pvPortMalloc
    queue.o(.text.xQueueGenericCreate) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.xQueueGenericCreate) refers to list.o(.text.vListInitialise) for vListInitialise
    queue.o(.text.xQueueGenericCreate) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.ARM.exidx.text.xQueueGenericCreate) refers to queue.o(.text.xQueueGenericCreate) for [Anonymous Symbol]
    queue.o(.text.xQueueCreateMutex) refers to heap_4.o(.text.pvPortMalloc) for pvPortMalloc
    queue.o(.text.xQueueCreateMutex) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.xQueueCreateMutex) refers to list.o(.text.vListInitialise) for vListInitialise
    queue.o(.text.xQueueCreateMutex) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.text.xQueueCreateMutex) refers to queue.o(.text.xQueueGenericSend) for xQueueGenericSend
    queue.o(.ARM.exidx.text.xQueueCreateMutex) refers to queue.o(.text.xQueueCreateMutex) for [Anonymous Symbol]
    queue.o(.text.xQueueGetMutexHolder) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.xQueueGetMutexHolder) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.ARM.exidx.text.xQueueGetMutexHolder) refers to queue.o(.text.xQueueGetMutexHolder) for [Anonymous Symbol]
    queue.o(.ARM.exidx.text.xQueueGetMutexHolderFromISR) refers to queue.o(.text.xQueueGetMutexHolderFromISR) for [Anonymous Symbol]
    queue.o(.text.xQueueGiveMutexRecursive) refers to tasks.o(.text.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    queue.o(.text.xQueueGiveMutexRecursive) refers to queue.o(.text.xQueueGenericSend) for xQueueGenericSend
    queue.o(.ARM.exidx.text.xQueueGiveMutexRecursive) refers to queue.o(.text.xQueueGiveMutexRecursive) for [Anonymous Symbol]
    queue.o(.text.xQueueGenericSend) refers to tasks.o(.text.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(.text.xQueueGenericSend) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.xQueueGenericSend) refers to queue.o(.text.prvUnlockQueue) for prvUnlockQueue
    queue.o(.text.xQueueGenericSend) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    queue.o(.text.xQueueGenericSend) refers to tasks.o(.text.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(.text.xQueueGenericSend) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.text.xQueueGenericSend) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(.text.xQueueGenericSend) refers to tasks.o(.text.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(.text.xQueueGenericSend) refers to tasks.o(.text.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(.text.xQueueGenericSend) refers to port.o(.text.vPortYield) for vPortYield
    queue.o(.text.xQueueGenericSend) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(.text.xQueueGenericSend) refers to tasks.o(.text.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(.text.xQueueGenericSend) refers to tasks.o(.text.xTaskPriorityDisinherit) for xTaskPriorityDisinherit
    queue.o(.ARM.exidx.text.xQueueGenericSend) refers to queue.o(.text.xQueueGenericSend) for [Anonymous Symbol]
    queue.o(.text.xQueueTakeMutexRecursive) refers to tasks.o(.text.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    queue.o(.text.xQueueTakeMutexRecursive) refers to queue.o(.text.xQueueSemaphoreTake) for xQueueSemaphoreTake
    queue.o(.ARM.exidx.text.xQueueTakeMutexRecursive) refers to queue.o(.text.xQueueTakeMutexRecursive) for [Anonymous Symbol]
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(.text.xQueueSemaphoreTake) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.pvTaskIncrementMutexHeldCount) for pvTaskIncrementMutexHeldCount
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(.text.xQueueSemaphoreTake) refers to port.o(.text.vPortYield) for vPortYield
    queue.o(.text.xQueueSemaphoreTake) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.text.xQueueSemaphoreTake) refers to queue.o(.text.prvUnlockQueue) for prvUnlockQueue
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.xTaskPriorityInherit) for xTaskPriorityInherit
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(.text.xQueueSemaphoreTake) refers to tasks.o(.text.vTaskPriorityDisinheritAfterTimeout) for vTaskPriorityDisinheritAfterTimeout
    queue.o(.ARM.exidx.text.xQueueSemaphoreTake) refers to queue.o(.text.xQueueSemaphoreTake) for [Anonymous Symbol]
    queue.o(.text.xQueueCreateCountingSemaphore) refers to queue.o(.text.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(.ARM.exidx.text.xQueueCreateCountingSemaphore) refers to queue.o(.text.xQueueCreateCountingSemaphore) for [Anonymous Symbol]
    queue.o(.text.prvUnlockQueue) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.prvUnlockQueue) refers to tasks.o(.text.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(.text.prvUnlockQueue) refers to tasks.o(.text.vTaskMissedYield) for vTaskMissedYield
    queue.o(.text.prvUnlockQueue) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.ARM.exidx.text.prvUnlockQueue) refers to queue.o(.text.prvUnlockQueue) for [Anonymous Symbol]
    queue.o(.text.xQueueGenericSendFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    queue.o(.text.xQueueGenericSendFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    queue.o(.text.xQueueGenericSendFromISR) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(.text.xQueueGenericSendFromISR) refers to tasks.o(.text.xTaskPriorityDisinherit) for xTaskPriorityDisinherit
    queue.o(.text.xQueueGenericSendFromISR) refers to tasks.o(.text.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(.text.xQueueGenericSendFromISR) refers to tasks.o(.text.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(.ARM.exidx.text.xQueueGenericSendFromISR) refers to queue.o(.text.xQueueGenericSendFromISR) for [Anonymous Symbol]
    queue.o(.text.xQueueGiveFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    queue.o(.text.xQueueGiveFromISR) refers to tasks.o(.text.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(.text.xQueueGiveFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    queue.o(.text.xQueueGiveFromISR) refers to tasks.o(.text.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(.ARM.exidx.text.xQueueGiveFromISR) refers to queue.o(.text.xQueueGiveFromISR) for [Anonymous Symbol]
    queue.o(.text.xQueueReceive) refers to tasks.o(.text.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(.text.xQueueReceive) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.xQueueReceive) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(.text.xQueueReceive) refers to tasks.o(.text.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(.text.xQueueReceive) refers to port.o(.text.vPortYield) for vPortYield
    queue.o(.text.xQueueReceive) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.text.xQueueReceive) refers to queue.o(.text.prvUnlockQueue) for prvUnlockQueue
    queue.o(.text.xQueueReceive) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    queue.o(.text.xQueueReceive) refers to tasks.o(.text.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(.text.xQueueReceive) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(.text.xQueueReceive) refers to tasks.o(.text.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(.text.xQueueReceive) refers to tasks.o(.text.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(.ARM.exidx.text.xQueueReceive) refers to queue.o(.text.xQueueReceive) for [Anonymous Symbol]
    queue.o(.text.xQueuePeek) refers to tasks.o(.text.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(.text.xQueuePeek) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.xQueuePeek) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(.text.xQueuePeek) refers to tasks.o(.text.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(.text.xQueuePeek) refers to port.o(.text.vPortYield) for vPortYield
    queue.o(.text.xQueuePeek) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.text.xQueuePeek) refers to queue.o(.text.prvUnlockQueue) for prvUnlockQueue
    queue.o(.text.xQueuePeek) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    queue.o(.text.xQueuePeek) refers to tasks.o(.text.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(.text.xQueuePeek) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(.text.xQueuePeek) refers to tasks.o(.text.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(.text.xQueuePeek) refers to tasks.o(.text.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(.ARM.exidx.text.xQueuePeek) refers to queue.o(.text.xQueuePeek) for [Anonymous Symbol]
    queue.o(.text.xQueueReceiveFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    queue.o(.text.xQueueReceiveFromISR) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(.text.xQueueReceiveFromISR) refers to tasks.o(.text.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(.text.xQueueReceiveFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    queue.o(.text.xQueueReceiveFromISR) refers to tasks.o(.text.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(.ARM.exidx.text.xQueueReceiveFromISR) refers to queue.o(.text.xQueueReceiveFromISR) for [Anonymous Symbol]
    queue.o(.text.xQueuePeekFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    queue.o(.text.xQueuePeekFromISR) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(.text.xQueuePeekFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    queue.o(.ARM.exidx.text.xQueuePeekFromISR) refers to queue.o(.text.xQueuePeekFromISR) for [Anonymous Symbol]
    queue.o(.text.uxQueueMessagesWaiting) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.uxQueueMessagesWaiting) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.ARM.exidx.text.uxQueueMessagesWaiting) refers to queue.o(.text.uxQueueMessagesWaiting) for [Anonymous Symbol]
    queue.o(.text.uxQueueSpacesAvailable) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.uxQueueSpacesAvailable) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.ARM.exidx.text.uxQueueSpacesAvailable) refers to queue.o(.text.uxQueueSpacesAvailable) for [Anonymous Symbol]
    queue.o(.ARM.exidx.text.uxQueueMessagesWaitingFromISR) refers to queue.o(.text.uxQueueMessagesWaitingFromISR) for [Anonymous Symbol]
    queue.o(.text.vQueueDelete) refers to heap_4.o(.text.vPortFree) for vPortFree
    queue.o(.text.vQueueDelete) refers to queue.o(.bss.xQueueRegistry) for xQueueRegistry
    queue.o(.ARM.exidx.text.vQueueDelete) refers to queue.o(.text.vQueueDelete) for [Anonymous Symbol]
    queue.o(.text.vQueueUnregisterQueue) refers to queue.o(.bss.xQueueRegistry) for xQueueRegistry
    queue.o(.ARM.exidx.text.vQueueUnregisterQueue) refers to queue.o(.text.vQueueUnregisterQueue) for [Anonymous Symbol]
    queue.o(.ARM.exidx.text.uxQueueGetQueueNumber) refers to queue.o(.text.uxQueueGetQueueNumber) for [Anonymous Symbol]
    queue.o(.ARM.exidx.text.vQueueSetQueueNumber) refers to queue.o(.text.vQueueSetQueueNumber) for [Anonymous Symbol]
    queue.o(.ARM.exidx.text.ucQueueGetQueueType) refers to queue.o(.text.ucQueueGetQueueType) for [Anonymous Symbol]
    queue.o(.ARM.exidx.text.xQueueIsQueueEmptyFromISR) refers to queue.o(.text.xQueueIsQueueEmptyFromISR) for [Anonymous Symbol]
    queue.o(.ARM.exidx.text.xQueueIsQueueFullFromISR) refers to queue.o(.text.xQueueIsQueueFullFromISR) for [Anonymous Symbol]
    queue.o(.text.vQueueAddToRegistry) refers to queue.o(.bss.xQueueRegistry) for xQueueRegistry
    queue.o(.ARM.exidx.text.vQueueAddToRegistry) refers to queue.o(.text.vQueueAddToRegistry) for [Anonymous Symbol]
    queue.o(.text.pcQueueGetName) refers to queue.o(.bss.xQueueRegistry) for xQueueRegistry
    queue.o(.ARM.exidx.text.pcQueueGetName) refers to queue.o(.text.pcQueueGetName) for [Anonymous Symbol]
    queue.o(.text.vQueueWaitForMessageRestricted) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    queue.o(.text.vQueueWaitForMessageRestricted) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    queue.o(.text.vQueueWaitForMessageRestricted) refers to queue.o(.text.prvUnlockQueue) for prvUnlockQueue
    queue.o(.text.vQueueWaitForMessageRestricted) refers to tasks.o(.text.vTaskPlaceOnEventListRestricted) for vTaskPlaceOnEventListRestricted
    queue.o(.ARM.exidx.text.vQueueWaitForMessageRestricted) refers to queue.o(.text.vQueueWaitForMessageRestricted) for [Anonymous Symbol]
    tasks.o(.text.xTaskCreate) refers to heap_4.o(.text.pvPortMalloc) for pvPortMalloc
    tasks.o(.text.xTaskCreate) refers to memseta.o(.text) for __aeabi_memclr
    tasks.o(.text.xTaskCreate) refers to heap_4.o(.text.vPortFree) for vPortFree
    tasks.o(.text.xTaskCreate) refers to list.o(.text.vListInitialiseItem) for vListInitialiseItem
    tasks.o(.text.xTaskCreate) refers to port.o(.text.pxPortInitialiseStack) for pxPortInitialiseStack
    tasks.o(.text.xTaskCreate) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.xTaskCreate) refers to list.o(.text.vListInitialise) for vListInitialise
    tasks.o(.text.xTaskCreate) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.xTaskCreate) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.xTaskCreate) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.xTaskCreate) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.xTaskCreate) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.xTaskCreate) refers to tasks.o(.bss..L_MergedGlobals.1) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskCreate) refers to tasks.o(.text.xTaskCreate) for [Anonymous Symbol]
    tasks.o(.text.vTaskDelete) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.vTaskDelete) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.vTaskDelete) refers to list.o(.text.vListInsertEnd) for vListInsertEnd
    tasks.o(.text.vTaskDelete) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.vTaskDelete) refers to heap_4.o(.text.vPortFree) for vPortFree
    tasks.o(.text.vTaskDelete) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.vTaskDelete) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.vTaskDelete) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskDelete) refers to tasks.o(.text.vTaskDelete) for [Anonymous Symbol]
    tasks.o(.text.xTaskDelayUntil) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.xTaskDelayUntil) refers to list.o(.text.vListInsert) for vListInsert
    tasks.o(.text.xTaskDelayUntil) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    tasks.o(.text.xTaskDelayUntil) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.xTaskDelayUntil) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.xTaskDelayUntil) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.xTaskDelayUntil) refers to tasks.o(.text.xTaskDelayUntil) for [Anonymous Symbol]
    tasks.o(.text.vTaskSuspendAll) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskSuspendAll) refers to tasks.o(.text.vTaskSuspendAll) for [Anonymous Symbol]
    tasks.o(.text.xTaskResumeAll) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.xTaskResumeAll) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.xTaskResumeAll) refers to tasks.o(.text.xTaskIncrementTick) for xTaskIncrementTick
    tasks.o(.text.xTaskResumeAll) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.xTaskResumeAll) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.xTaskResumeAll) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.xTaskResumeAll) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.xTaskResumeAll) refers to tasks.o(.text.xTaskResumeAll) for [Anonymous Symbol]
    tasks.o(.text.vTaskDelay) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.vTaskDelay) refers to list.o(.text.vListInsert) for vListInsert
    tasks.o(.text.vTaskDelay) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    tasks.o(.text.vTaskDelay) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.vTaskDelay) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.vTaskDelay) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.vTaskDelay) refers to tasks.o(.text.vTaskDelay) for [Anonymous Symbol]
    tasks.o(.text.eTaskGetState) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.eTaskGetState) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.eTaskGetState) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.eTaskGetState) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.eTaskGetState) refers to tasks.o(.text.eTaskGetState) for [Anonymous Symbol]
    tasks.o(.text.uxTaskPriorityGet) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.uxTaskPriorityGet) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.uxTaskPriorityGet) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.uxTaskPriorityGet) refers to tasks.o(.text.uxTaskPriorityGet) for [Anonymous Symbol]
    tasks.o(.text.uxTaskPriorityGetFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    tasks.o(.text.uxTaskPriorityGetFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    tasks.o(.text.uxTaskPriorityGetFromISR) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.uxTaskPriorityGetFromISR) refers to tasks.o(.text.uxTaskPriorityGetFromISR) for [Anonymous Symbol]
    tasks.o(.text.vTaskPrioritySet) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.vTaskPrioritySet) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.vTaskPrioritySet) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.vTaskPrioritySet) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.vTaskPrioritySet) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.vTaskPrioritySet) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.vTaskPrioritySet) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskPrioritySet) refers to tasks.o(.text.vTaskPrioritySet) for [Anonymous Symbol]
    tasks.o(.text.vTaskSuspend) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.vTaskSuspend) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.vTaskSuspend) refers to list.o(.text.vListInsertEnd) for vListInsertEnd
    tasks.o(.text.vTaskSuspend) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.vTaskSuspend) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.vTaskSuspend) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.vTaskSuspend) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.vTaskSuspend) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskSuspend) refers to tasks.o(.text.vTaskSuspend) for [Anonymous Symbol]
    tasks.o(.text.vTaskSwitchContext) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.vTaskSwitchContext) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.vTaskSwitchContext) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.vTaskSwitchContext) refers to tasks.o(.text.vTaskSwitchContext) for [Anonymous Symbol]
    tasks.o(.text.vTaskResume) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.vTaskResume) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.vTaskResume) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.vTaskResume) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.vTaskResume) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.vTaskResume) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.vTaskResume) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskResume) refers to tasks.o(.text.vTaskResume) for [Anonymous Symbol]
    tasks.o(.text.xTaskResumeFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    tasks.o(.text.xTaskResumeFromISR) refers to list.o(.text.vListInsertEnd) for vListInsertEnd
    tasks.o(.text.xTaskResumeFromISR) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.xTaskResumeFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    tasks.o(.text.xTaskResumeFromISR) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.xTaskResumeFromISR) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.xTaskResumeFromISR) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskResumeFromISR) refers to tasks.o(.text.xTaskResumeFromISR) for [Anonymous Symbol]
    tasks.o(.text.vTaskStartScheduler) refers to tasks.o(.text.xTaskCreate) for xTaskCreate
    tasks.o(.text.vTaskStartScheduler) refers to timers.o(.text.xTimerCreateTimerTask) for xTimerCreateTimerTask
    tasks.o(.text.vTaskStartScheduler) refers to port.o(.text.xPortStartScheduler) for xPortStartScheduler
    tasks.o(.text.vTaskStartScheduler) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.vTaskStartScheduler) refers to tasks.o(.text.prvIdleTask) for prvIdleTask
    tasks.o(.text.vTaskStartScheduler) refers to tasks.o(.rodata.uxTopUsedPriority) for uxTopUsedPriority
    tasks.o(.ARM.exidx.text.vTaskStartScheduler) refers to tasks.o(.text.vTaskStartScheduler) for [Anonymous Symbol]
    tasks.o(.text.prvIdleTask) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.prvIdleTask) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.prvIdleTask) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.prvIdleTask) refers to heap_4.o(.text.vPortFree) for vPortFree
    tasks.o(.text.prvIdleTask) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.prvIdleTask) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.prvIdleTask) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.prvIdleTask) refers to tasks.o(.text.prvIdleTask) for [Anonymous Symbol]
    tasks.o(.text.vTaskEndScheduler) refers to port.o(.text.vPortEndScheduler) for vPortEndScheduler
    tasks.o(.text.vTaskEndScheduler) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskEndScheduler) refers to tasks.o(.text.vTaskEndScheduler) for [Anonymous Symbol]
    tasks.o(.text.xTaskIncrementTick) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.xTaskIncrementTick) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.xTaskIncrementTick) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.xTaskIncrementTick) refers to tasks.o(.text.xTaskIncrementTick) for [Anonymous Symbol]
    tasks.o(.text.xTaskGetTickCount) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskGetTickCount) refers to tasks.o(.text.xTaskGetTickCount) for [Anonymous Symbol]
    tasks.o(.text.xTaskGetTickCountFromISR) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskGetTickCountFromISR) refers to tasks.o(.text.xTaskGetTickCountFromISR) for [Anonymous Symbol]
    tasks.o(.text.uxTaskGetNumberOfTasks) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.uxTaskGetNumberOfTasks) refers to tasks.o(.text.uxTaskGetNumberOfTasks) for [Anonymous Symbol]
    tasks.o(.text.pcTaskGetName) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.pcTaskGetName) refers to tasks.o(.text.pcTaskGetName) for [Anonymous Symbol]
    tasks.o(.text.uxTaskGetSystemState) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    tasks.o(.text.uxTaskGetSystemState) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.uxTaskGetSystemState) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.uxTaskGetSystemState) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.uxTaskGetSystemState) refers to tasks.o(.text.uxTaskGetSystemState) for [Anonymous Symbol]
    tasks.o(.text.xTaskCatchUpTicks) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.xTaskCatchUpTicks) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.xTaskCatchUpTicks) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    tasks.o(.text.xTaskCatchUpTicks) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskCatchUpTicks) refers to tasks.o(.text.xTaskCatchUpTicks) for [Anonymous Symbol]
    tasks.o(.text.vTaskPlaceOnEventList) refers to list.o(.text.vListInsert) for vListInsert
    tasks.o(.text.vTaskPlaceOnEventList) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.vTaskPlaceOnEventList) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.vTaskPlaceOnEventList) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskPlaceOnEventList) refers to tasks.o(.text.vTaskPlaceOnEventList) for [Anonymous Symbol]
    tasks.o(.text.vTaskPlaceOnUnorderedEventList) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.vTaskPlaceOnUnorderedEventList) refers to list.o(.text.vListInsert) for vListInsert
    tasks.o(.text.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.text.vTaskPlaceOnUnorderedEventList) for [Anonymous Symbol]
    tasks.o(.text.vTaskPlaceOnEventListRestricted) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.vTaskPlaceOnEventListRestricted) refers to list.o(.text.vListInsert) for vListInsert
    tasks.o(.text.vTaskPlaceOnEventListRestricted) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.vTaskPlaceOnEventListRestricted) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskPlaceOnEventListRestricted) refers to tasks.o(.text.vTaskPlaceOnEventListRestricted) for [Anonymous Symbol]
    tasks.o(.text.xTaskRemoveFromEventList) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.xTaskRemoveFromEventList) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.xTaskRemoveFromEventList) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.xTaskRemoveFromEventList) refers to tasks.o(.text.xTaskRemoveFromEventList) for [Anonymous Symbol]
    tasks.o(.text.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.text.vTaskRemoveFromUnorderedEventList) for [Anonymous Symbol]
    tasks.o(.text.vTaskSetTimeOutState) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.vTaskSetTimeOutState) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.vTaskSetTimeOutState) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskSetTimeOutState) refers to tasks.o(.text.vTaskSetTimeOutState) for [Anonymous Symbol]
    tasks.o(.text.vTaskInternalSetTimeOutState) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskInternalSetTimeOutState) refers to tasks.o(.text.vTaskInternalSetTimeOutState) for [Anonymous Symbol]
    tasks.o(.text.xTaskCheckForTimeOut) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.xTaskCheckForTimeOut) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.xTaskCheckForTimeOut) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskCheckForTimeOut) refers to tasks.o(.text.xTaskCheckForTimeOut) for [Anonymous Symbol]
    tasks.o(.text.vTaskMissedYield) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskMissedYield) refers to tasks.o(.text.vTaskMissedYield) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.uxTaskGetTaskNumber) refers to tasks.o(.text.uxTaskGetTaskNumber) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskSetTaskNumber) refers to tasks.o(.text.vTaskSetTaskNumber) for [Anonymous Symbol]
    tasks.o(.text.vTaskGetInfo) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.vTaskGetInfo) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.vTaskGetInfo) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    tasks.o(.text.vTaskGetInfo) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.vTaskGetInfo) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskGetInfo) refers to tasks.o(.text.vTaskGetInfo) for [Anonymous Symbol]
    tasks.o(.text.uxTaskGetStackHighWaterMark) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.uxTaskGetStackHighWaterMark) refers to tasks.o(.text.uxTaskGetStackHighWaterMark) for [Anonymous Symbol]
    tasks.o(.text.xTaskGetCurrentTaskHandle) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.xTaskGetCurrentTaskHandle) refers to tasks.o(.text.xTaskGetCurrentTaskHandle) for [Anonymous Symbol]
    tasks.o(.text.xTaskGetSchedulerState) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskGetSchedulerState) refers to tasks.o(.text.xTaskGetSchedulerState) for [Anonymous Symbol]
    tasks.o(.text.xTaskPriorityInherit) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.xTaskPriorityInherit) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.xTaskPriorityInherit) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.xTaskPriorityInherit) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskPriorityInherit) refers to tasks.o(.text.xTaskPriorityInherit) for [Anonymous Symbol]
    tasks.o(.text.xTaskPriorityDisinherit) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.xTaskPriorityDisinherit) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.xTaskPriorityDisinherit) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.xTaskPriorityDisinherit) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskPriorityDisinherit) refers to tasks.o(.text.xTaskPriorityDisinherit) for [Anonymous Symbol]
    tasks.o(.text.vTaskPriorityDisinheritAfterTimeout) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.text.vTaskPriorityDisinheritAfterTimeout) for [Anonymous Symbol]
    tasks.o(.text.uxTaskResetEventItemValue) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.uxTaskResetEventItemValue) refers to tasks.o(.text.uxTaskResetEventItemValue) for [Anonymous Symbol]
    tasks.o(.text.pvTaskIncrementMutexHeldCount) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.pvTaskIncrementMutexHeldCount) refers to tasks.o(.text.pvTaskIncrementMutexHeldCount) for [Anonymous Symbol]
    tasks.o(.text.ulTaskGenericNotifyTake) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.ulTaskGenericNotifyTake) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.ulTaskGenericNotifyTake) refers to list.o(.text.vListInsert) for vListInsert
    tasks.o(.text.ulTaskGenericNotifyTake) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.ulTaskGenericNotifyTake) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.ulTaskGenericNotifyTake) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.ulTaskGenericNotifyTake) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.ulTaskGenericNotifyTake) refers to tasks.o(.text.ulTaskGenericNotifyTake) for [Anonymous Symbol]
    tasks.o(.text.xTaskGenericNotifyWait) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.xTaskGenericNotifyWait) refers to list.o(.text.uxListRemove) for uxListRemove
    tasks.o(.text.xTaskGenericNotifyWait) refers to list.o(.text.vListInsert) for vListInsert
    tasks.o(.text.xTaskGenericNotifyWait) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.xTaskGenericNotifyWait) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.xTaskGenericNotifyWait) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.text.xTaskGenericNotifyWait) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.ARM.exidx.text.xTaskGenericNotifyWait) refers to tasks.o(.text.xTaskGenericNotifyWait) for [Anonymous Symbol]
    tasks.o(.text.xTaskGenericNotify) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.xTaskGenericNotify) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.xTaskGenericNotify) refers to port.o(.text.vPortYield) for vPortYield
    tasks.o(.text.xTaskGenericNotify) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.xTaskGenericNotify) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.xTaskGenericNotify) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.xTaskGenericNotify) refers to tasks.o(.text.xTaskGenericNotify) for [Anonymous Symbol]
    tasks.o(.text.xTaskGenericNotifyFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    tasks.o(.text.xTaskGenericNotifyFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    tasks.o(.text.xTaskGenericNotifyFromISR) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.xTaskGenericNotifyFromISR) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.xTaskGenericNotifyFromISR) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.xTaskGenericNotifyFromISR) refers to tasks.o(.text.xTaskGenericNotifyFromISR) for [Anonymous Symbol]
    tasks.o(.text.vTaskGenericNotifyGiveFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    tasks.o(.text.vTaskGenericNotifyGiveFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    tasks.o(.text.vTaskGenericNotifyGiveFromISR) refers to tasks.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    tasks.o(.text.vTaskGenericNotifyGiveFromISR) refers to tasks.o(.bss.pxReadyTasksLists) for [Anonymous Symbol]
    tasks.o(.text.vTaskGenericNotifyGiveFromISR) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.vTaskGenericNotifyGiveFromISR) refers to tasks.o(.text.vTaskGenericNotifyGiveFromISR) for [Anonymous Symbol]
    tasks.o(.text.xTaskGenericNotifyStateClear) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.xTaskGenericNotifyStateClear) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.xTaskGenericNotifyStateClear) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.xTaskGenericNotifyStateClear) refers to tasks.o(.text.xTaskGenericNotifyStateClear) for [Anonymous Symbol]
    tasks.o(.text.ulTaskGenericNotifyValueClear) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    tasks.o(.text.ulTaskGenericNotifyValueClear) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    tasks.o(.text.ulTaskGenericNotifyValueClear) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    tasks.o(.ARM.exidx.text.ulTaskGenericNotifyValueClear) refers to tasks.o(.text.ulTaskGenericNotifyValueClear) for [Anonymous Symbol]
    timers.o(.text.xTimerCreateTimerTask) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    timers.o(.text.xTimerCreateTimerTask) refers to list.o(.text.vListInitialise) for vListInitialise
    timers.o(.text.xTimerCreateTimerTask) refers to queue.o(.text.xQueueGenericCreate) for xQueueGenericCreate
    timers.o(.text.xTimerCreateTimerTask) refers to queue.o(.text.vQueueAddToRegistry) for vQueueAddToRegistry
    timers.o(.text.xTimerCreateTimerTask) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    timers.o(.text.xTimerCreateTimerTask) refers to tasks.o(.text.xTaskCreate) for xTaskCreate
    timers.o(.text.xTimerCreateTimerTask) refers to timers.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    timers.o(.text.xTimerCreateTimerTask) refers to timers.o(.rodata.str1.1) for [Anonymous Symbol]
    timers.o(.text.xTimerCreateTimerTask) refers to timers.o(.text.prvTimerTask) for prvTimerTask
    timers.o(.ARM.exidx.text.xTimerCreateTimerTask) refers to timers.o(.text.xTimerCreateTimerTask) for [Anonymous Symbol]
    timers.o(.text.prvTimerTask) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    timers.o(.text.prvTimerTask) refers to tasks.o(.text.xTaskGetTickCount) for xTaskGetTickCount
    timers.o(.text.prvTimerTask) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    timers.o(.text.prvTimerTask) refers to list.o(.text.uxListRemove) for uxListRemove
    timers.o(.text.prvTimerTask) refers to list.o(.text.vListInsert) for vListInsert
    timers.o(.text.prvTimerTask) refers to queue.o(.text.vQueueWaitForMessageRestricted) for vQueueWaitForMessageRestricted
    timers.o(.text.prvTimerTask) refers to port.o(.text.vPortYield) for vPortYield
    timers.o(.text.prvTimerTask) refers to queue.o(.text.xQueueReceive) for xQueueReceive
    timers.o(.text.prvTimerTask) refers to heap_4.o(.text.vPortFree) for vPortFree
    timers.o(.text.prvTimerTask) refers to timers.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.prvTimerTask) refers to timers.o(.text.prvTimerTask) for [Anonymous Symbol]
    timers.o(.text.xTimerCreate) refers to heap_4.o(.text.pvPortMalloc) for pvPortMalloc
    timers.o(.text.xTimerCreate) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    timers.o(.text.xTimerCreate) refers to list.o(.text.vListInitialise) for vListInitialise
    timers.o(.text.xTimerCreate) refers to queue.o(.text.xQueueGenericCreate) for xQueueGenericCreate
    timers.o(.text.xTimerCreate) refers to queue.o(.text.vQueueAddToRegistry) for vQueueAddToRegistry
    timers.o(.text.xTimerCreate) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    timers.o(.text.xTimerCreate) refers to list.o(.text.vListInitialiseItem) for vListInitialiseItem
    timers.o(.text.xTimerCreate) refers to timers.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    timers.o(.text.xTimerCreate) refers to timers.o(.rodata.str1.1) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.xTimerCreate) refers to timers.o(.text.xTimerCreate) for [Anonymous Symbol]
    timers.o(.text.xTimerGenericCommand) refers to tasks.o(.text.xTaskGetSchedulerState) for xTaskGetSchedulerState
    timers.o(.text.xTimerGenericCommand) refers to queue.o(.text.xQueueGenericSend) for xQueueGenericSend
    timers.o(.text.xTimerGenericCommand) refers to queue.o(.text.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(.text.xTimerGenericCommand) refers to timers.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.xTimerGenericCommand) refers to timers.o(.text.xTimerGenericCommand) for [Anonymous Symbol]
    timers.o(.text.xTimerGetTimerDaemonTaskHandle) refers to timers.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.xTimerGetTimerDaemonTaskHandle) refers to timers.o(.text.xTimerGetTimerDaemonTaskHandle) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.xTimerGetPeriod) refers to timers.o(.text.xTimerGetPeriod) for [Anonymous Symbol]
    timers.o(.text.vTimerSetReloadMode) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    timers.o(.text.vTimerSetReloadMode) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    timers.o(.ARM.exidx.text.vTimerSetReloadMode) refers to timers.o(.text.vTimerSetReloadMode) for [Anonymous Symbol]
    timers.o(.text.xTimerGetReloadMode) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    timers.o(.text.xTimerGetReloadMode) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    timers.o(.ARM.exidx.text.xTimerGetReloadMode) refers to timers.o(.text.xTimerGetReloadMode) for [Anonymous Symbol]
    timers.o(.text.uxTimerGetReloadMode) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    timers.o(.text.uxTimerGetReloadMode) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    timers.o(.ARM.exidx.text.uxTimerGetReloadMode) refers to timers.o(.text.uxTimerGetReloadMode) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.xTimerGetExpiryTime) refers to timers.o(.text.xTimerGetExpiryTime) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.pcTimerGetName) refers to timers.o(.text.pcTimerGetName) for [Anonymous Symbol]
    timers.o(.text.xTimerIsTimerActive) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    timers.o(.text.xTimerIsTimerActive) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    timers.o(.ARM.exidx.text.xTimerIsTimerActive) refers to timers.o(.text.xTimerIsTimerActive) for [Anonymous Symbol]
    timers.o(.text.pvTimerGetTimerID) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    timers.o(.text.pvTimerGetTimerID) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    timers.o(.ARM.exidx.text.pvTimerGetTimerID) refers to timers.o(.text.pvTimerGetTimerID) for [Anonymous Symbol]
    timers.o(.text.vTimerSetTimerID) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    timers.o(.text.vTimerSetTimerID) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    timers.o(.ARM.exidx.text.vTimerSetTimerID) refers to timers.o(.text.vTimerSetTimerID) for [Anonymous Symbol]
    timers.o(.text.xTimerPendFunctionCallFromISR) refers to queue.o(.text.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(.text.xTimerPendFunctionCallFromISR) refers to timers.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.xTimerPendFunctionCallFromISR) refers to timers.o(.text.xTimerPendFunctionCallFromISR) for [Anonymous Symbol]
    timers.o(.text.xTimerPendFunctionCall) refers to queue.o(.text.xQueueGenericSend) for xQueueGenericSend
    timers.o(.text.xTimerPendFunctionCall) refers to timers.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.xTimerPendFunctionCall) refers to timers.o(.text.xTimerPendFunctionCall) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.uxTimerGetTimerNumber) refers to timers.o(.text.uxTimerGetTimerNumber) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.vTimerSetTimerNumber) refers to timers.o(.text.vTimerSetTimerNumber) for [Anonymous Symbol]
    heap_4.o(.text.pvPortMalloc) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(.text.pvPortMalloc) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(.text.pvPortMalloc) refers to heap_4.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    heap_4.o(.text.pvPortMalloc) refers to heap_4.o(.bss.ucHeap) for [Anonymous Symbol]
    heap_4.o(.ARM.exidx.text.pvPortMalloc) refers to heap_4.o(.text.pvPortMalloc) for [Anonymous Symbol]
    heap_4.o(.text.vPortFree) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(.text.vPortFree) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(.text.vPortFree) refers to heap_4.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    heap_4.o(.ARM.exidx.text.vPortFree) refers to heap_4.o(.text.vPortFree) for [Anonymous Symbol]
    heap_4.o(.text.xPortGetFreeHeapSize) refers to heap_4.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    heap_4.o(.ARM.exidx.text.xPortGetFreeHeapSize) refers to heap_4.o(.text.xPortGetFreeHeapSize) for [Anonymous Symbol]
    heap_4.o(.text.xPortGetMinimumEverFreeHeapSize) refers to heap_4.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    heap_4.o(.ARM.exidx.text.xPortGetMinimumEverFreeHeapSize) refers to heap_4.o(.text.xPortGetMinimumEverFreeHeapSize) for [Anonymous Symbol]
    heap_4.o(.ARM.exidx.text.vPortInitialiseBlocks) refers to heap_4.o(.text.vPortInitialiseBlocks) for [Anonymous Symbol]
    heap_4.o(.text.pvPortCalloc) refers to llmul.o(.text) for __aeabi_lmul
    heap_4.o(.text.pvPortCalloc) refers to heap_4.o(.text.pvPortMalloc) for pvPortMalloc
    heap_4.o(.text.pvPortCalloc) refers to memseta.o(.text) for __aeabi_memclr
    heap_4.o(.ARM.exidx.text.pvPortCalloc) refers to heap_4.o(.text.pvPortCalloc) for [Anonymous Symbol]
    heap_4.o(.text.vPortGetHeapStats) refers to tasks.o(.text.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(.text.vPortGetHeapStats) refers to tasks.o(.text.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(.text.vPortGetHeapStats) refers to port.o(.text.vPortEnterCritical) for vPortEnterCritical
    heap_4.o(.text.vPortGetHeapStats) refers to port.o(.text.vPortExitCritical) for vPortExitCritical
    heap_4.o(.text.vPortGetHeapStats) refers to heap_4.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    heap_4.o(.ARM.exidx.text.vPortGetHeapStats) refers to heap_4.o(.text.vPortGetHeapStats) for [Anonymous Symbol]
    port.o(.text.pxPortInitialiseStack) refers to port.o(.text.prvTaskExitError) for prvTaskExitError
    port.o(.ARM.exidx.text.pxPortInitialiseStack) refers to port.o(.text.pxPortInitialiseStack) for [Anonymous Symbol]
    port.o(.text.prvTaskExitError) refers to port.o(.data.uxCriticalNesting) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.prvTaskExitError) refers to port.o(.text.prvTaskExitError) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.SVC_Handler) refers to port.o(.text.SVC_Handler) for [Anonymous Symbol]
    port.o(.text.xPortStartScheduler) refers to port.o(.text.vPortSetupTimerInterrupt) for vPortSetupTimerInterrupt
    port.o(.text.xPortStartScheduler) refers to port.o(.text.vPortStartFirstTask) for vPortStartFirstTask
    port.o(.text.xPortStartScheduler) refers to tasks.o(.text.vTaskSwitchContext) for vTaskSwitchContext
    port.o(.text.xPortStartScheduler) refers to port.o(.data.uxCriticalNesting) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.xPortStartScheduler) refers to port.o(.text.xPortStartScheduler) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.vPortSetupTimerInterrupt) refers to port.o(.text.vPortSetupTimerInterrupt) for [Anonymous Symbol]
    port.o(.text.vPortStartFirstTask) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    port.o(.ARM.exidx.text.vPortStartFirstTask) refers to port.o(.text.vPortStartFirstTask) for [Anonymous Symbol]
    port.o(.text.vPortEndScheduler) refers to port.o(.data.uxCriticalNesting) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.vPortEndScheduler) refers to port.o(.text.vPortEndScheduler) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.vPortYield) refers to port.o(.text.vPortYield) for [Anonymous Symbol]
    port.o(.text.vPortEnterCritical) refers to port.o(.data.uxCriticalNesting) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.vPortEnterCritical) refers to port.o(.text.vPortEnterCritical) for [Anonymous Symbol]
    port.o(.text.vPortExitCritical) refers to port.o(.data.uxCriticalNesting) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.vPortExitCritical) refers to port.o(.text.vPortExitCritical) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.ulSetInterruptMaskFromISR) refers to port.o(.text.ulSetInterruptMaskFromISR) for [Anonymous Symbol]
    port.o(.ARM.exidx.text.vClearInterruptMaskFromISR) refers to port.o(.text.vClearInterruptMaskFromISR) for [Anonymous Symbol]
    port.o(.text.PendSV_Handler) refers to tasks.o(.text.vTaskSwitchContext) for vTaskSwitchContext
    port.o(.text.PendSV_Handler) refers to tasks.o(.bss.pxCurrentTCB) for pxCurrentTCB
    port.o(.ARM.exidx.text.PendSV_Handler) refers to port.o(.text.PendSV_Handler) for [Anonymous Symbol]
    port.o(.text.SysTick_Handler) refers to port.o(.text.ulSetInterruptMaskFromISR) for ulSetInterruptMaskFromISR
    port.o(.text.SysTick_Handler) refers to tasks.o(.text.xTaskIncrementTick) for xTaskIncrementTick
    port.o(.text.SysTick_Handler) refers to port.o(.text.vClearInterruptMaskFromISR) for vClearInterruptMaskFromISR
    port.o(.ARM.exidx.text.SysTick_Handler) refers to port.o(.text.SysTick_Handler) for [Anonymous Symbol]
    startuptask.o(.text.StartupTask) refers to tasks.o(.text.xTaskCreate) for xTaskCreate
    startuptask.o(.text.StartupTask) refers to startuptask.o(.bss..L_MergedGlobals.7) for [Anonymous Symbol]
    startuptask.o(.text.StartupTask) refers to startuptask.o(.text.RGB_task) for RGB_task
    startuptask.o(.text.StartupTask) refers to startuptask.o(.text.OLED_task) for OLED_task
    startuptask.o(.text.StartupTask) refers to startuptask.o(.text.main_task) for main_task
    startuptask.o(.text.StartupTask) refers to startuptask.o(.text.line_task) for line_task
    startuptask.o(.text.StartupTask) refers to startuptask.o(.text.Set0_task) for Set0_task
    startuptask.o(.text.StartupTask) refers to startuptask.o(.text.circle_task) for circle_task
    startuptask.o(.text.StartupTask) refers to startuptask.o(.text.QEI_task) for QEI_task
    startuptask.o(.ARM.exidx.text.StartupTask) refers to startuptask.o(.text.StartupTask) for [Anonymous Symbol]
    startuptask.o(.text.RGB_task) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    startuptask.o(.ARM.exidx.text.RGB_task) refers to startuptask.o(.text.RGB_task) for [Anonymous Symbol]
    startuptask.o(.text.OLED_task) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    startuptask.o(.ARM.exidx.text.OLED_task) refers to startuptask.o(.text.OLED_task) for [Anonymous Symbol]
    startuptask.o(.text.main_task) refers to control.o(.text.PID_Init) for PID_Init
    startuptask.o(.text.main_task) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    startuptask.o(.text.main_task) refers to control.o(.text.TI) for TI
    startuptask.o(.text.main_task) refers to emm_v5.o(.text.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    startuptask.o(.text.main_task) refers to emm_v5.o(.text.Emm_V5_En_Control) for Emm_V5_En_Control
    startuptask.o(.text.main_task) refers to emm_v5.o(.text.Emm_V5_Origin_Trigger_Return) for Emm_V5_Origin_Trigger_Return
    startuptask.o(.text.main_task) refers to emm_v5.o(.text.POS_Control) for POS_Control
    startuptask.o(.text.main_task) refers to ffltui.o(.text) for __aeabi_ui2f
    startuptask.o(.text.main_task) refers to fflti.o(.text) for __aeabi_i2f
    startuptask.o(.text.main_task) refers to control.o(.text.Erect_pid) for Erect_pid
    startuptask.o(.text.main_task) refers to fcmple.o(.text) for __aeabi_fcmple
    startuptask.o(.text.main_task) refers to emm_v5.o(.text.Speed_Control) for Speed_Control
    startuptask.o(.text.main_task) refers to fcmpge.o(.text) for __aeabi_fcmpge
    startuptask.o(.text.main_task) refers to fadd.o(.text) for __aeabi_fadd
    startuptask.o(.text.main_task) refers to fcmpgt.o(.text) for __aeabi_fcmpgt
    startuptask.o(.text.main_task) refers to fcmplt.o(.text) for __aeabi_fcmplt
    startuptask.o(.text.main_task) refers to startuptask.o(.bss..L_MergedGlobals.7) for [Anonymous Symbol]
    startuptask.o(.text.main_task) refers to startuptask.o(.data..L_MergedGlobals) for [Anonymous Symbol]
    startuptask.o(.text.main_task) refers to maixcam.o(.bss..L_MergedGlobals) for Camera_flag
    startuptask.o(.text.main_task) refers to control.o(.bss..L_MergedGlobals) for Serx
    startuptask.o(.ARM.exidx.text.main_task) refers to startuptask.o(.text.main_task) for [Anonymous Symbol]
    startuptask.o(.text.line_task) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    startuptask.o(.text.line_task) refers to control.o(.text.Line_Control) for Line_Control
    startuptask.o(.text.line_task) refers to ffltui.o(.text) for __aeabi_ui2f
    startuptask.o(.text.line_task) refers to fflti.o(.text) for __aeabi_i2f
    startuptask.o(.text.line_task) refers to fmul.o(.text) for __aeabi_fmul
    startuptask.o(.text.line_task) refers to fadd.o(.text) for __aeabi_fadd
    startuptask.o(.text.line_task) refers to fcmpge.o(.text) for __aeabi_fcmpge
    startuptask.o(.text.line_task) refers to motor.o(.text.Motor_Write) for Motor_Write
    startuptask.o(.text.line_task) refers to startuptask.o(.bss..L_MergedGlobals.7) for [Anonymous Symbol]
    startuptask.o(.text.line_task) refers to startuptask.o(.data.car_go_dir) for car_go_dir
    startuptask.o(.ARM.exidx.text.line_task) refers to startuptask.o(.text.line_task) for [Anonymous Symbol]
    startuptask.o(.text.Set0_task) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    startuptask.o(.text.Set0_task) refers to motor.o(.text.Motor_Write) for Motor_Write
    startuptask.o(.text.Set0_task) refers to emm_v5.o(.text.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    startuptask.o(.text.Set0_task) refers to maixcam.o(.text.MaixCam_Send_XY) for MaixCam_Send_XY
    startuptask.o(.text.Set0_task) refers to startuptask.o(.bss..L_MergedGlobals.7) for [Anonymous Symbol]
    startuptask.o(.ARM.exidx.text.Set0_task) refers to startuptask.o(.text.Set0_task) for [Anonymous Symbol]
    startuptask.o(.text.circle_task) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    startuptask.o(.ARM.exidx.text.circle_task) refers to startuptask.o(.text.circle_task) for [Anonymous Symbol]
    startuptask.o(.text.QEI_task) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    startuptask.o(.text.QEI_task) refers to enconder.o(.text.Get_Encoder) for Get_Encoder
    startuptask.o(.text.QEI_task) refers to startuptask.o(.bss..L_MergedGlobals.7) for [Anonymous Symbol]
    startuptask.o(.ARM.exidx.text.QEI_task) refers to startuptask.o(.text.QEI_task) for [Anonymous Symbol]
    startuptask.o(.ARM.exidx.text.vApplicationStackOverflowHook) refers to startuptask.o(.text.vApplicationStackOverflowHook) for [Anonymous Symbol]
    startuptask.o(.ARM.exidx.text.vApplicationMallocFailedHook) refers to startuptask.o(.text.vApplicationMallocFailedHook) for [Anonymous Symbol]
    startuptask.o(.ARM.exidx.text.vApplicationIdleHook) refers to startuptask.o(.text.vApplicationIdleHook) for [Anonymous Symbol]
    startuptask.o(.ARM.exidx.text.vApplicationTickHook) refers to startuptask.o(.text.vApplicationTickHook) for [Anonymous Symbol]
    ws2812b.o(.ARM.exidx.text.WS2812_RESET) refers to ws2812b.o(.text.WS2812_RESET) for [Anonymous Symbol]
    ws2812b.o(.text.WS2812_Set_Color) refers to ws2812b.o(.bss.LedsArray) for [Anonymous Symbol]
    ws2812b.o(.ARM.exidx.text.WS2812_Set_Color) refers to ws2812b.o(.text.WS2812_Set_Color) for [Anonymous Symbol]
    ws2812b.o(.text.WS2812_Send_Array) refers to ws2812b.o(.text.WS2812_RESET) for WS2812_RESET
    ws2812b.o(.text.WS2812_Send_Array) refers to ws2812b.o(.bss.LedsArray) for [Anonymous Symbol]
    ws2812b.o(.ARM.exidx.text.WS2812_Send_Array) refers to ws2812b.o(.text.WS2812_Send_Array) for [Anonymous Symbol]
    servo.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue2) refers to servo.o(.text.DL_Timer_setCaptureCompareValue2) for [Anonymous Symbol]
    servo.o(.text.Set_Servo) refers to fcmpgt.o(.text) for __aeabi_fcmpgt
    servo.o(.text.Set_Servo) refers to fdiv.o(.text) for __aeabi_fdiv
    servo.o(.text.Set_Servo) refers to fmul.o(.text) for __aeabi_fmul
    servo.o(.text.Set_Servo) refers to fadd.o(.text) for __aeabi_fadd
    servo.o(.text.Set_Servo) refers to ffixi.o(.text) for __aeabi_f2iz
    servo.o(.text.Set_Servo) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    servo.o(.ARM.exidx.text.Set_Servo) refers to servo.o(.text.Set_Servo) for [Anonymous Symbol]
    servo.o(.text.Set_Servo_raw) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    servo.o(.ARM.exidx.text.Set_Servo_raw) refers to servo.o(.text.Set_Servo_raw) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.delay) refers to oled.o(.text.delay) for [Anonymous Symbol]
    oled.o(.text.IIC_Start) refers to led_key.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.IIC_Start) refers to oled.o(.text.IIC_Start) for [Anonymous Symbol]
    oled.o(.text.IIC_Stop) refers to led_key.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.IIC_Stop) refers to oled.o(.text.IIC_Stop) for [Anonymous Symbol]
    oled.o(.text.IIC_Ack) refers to led_key.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.IIC_Ack) refers to oled.o(.text.IIC_Ack) for [Anonymous Symbol]
    oled.o(.text.IIC_XIE) refers to led_key.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.IIC_XIE) refers to oled.o(.text.IIC_XIE) for [Anonymous Symbol]
    oled.o(.text.IIC_XIE_ML) refers to led_key.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.text.IIC_XIE_ML) refers to oled.o(.text.IIC_XIE) for IIC_XIE
    oled.o(.ARM.exidx.text.IIC_XIE_ML) refers to oled.o(.text.IIC_XIE_ML) for [Anonymous Symbol]
    oled.o(.text.IIC_XIE_DATA) refers to led_key.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.text.IIC_XIE_DATA) refers to oled.o(.text.IIC_XIE) for IIC_XIE
    oled.o(.ARM.exidx.text.IIC_XIE_DATA) refers to oled.o(.text.IIC_XIE_DATA) for [Anonymous Symbol]
    oled.o(.text.OLED_Set_Pos) refers to oled.o(.text.IIC_XIE_ML) for IIC_XIE_ML
    oled.o(.ARM.exidx.text.OLED_Set_Pos) refers to oled.o(.text.OLED_Set_Pos) for [Anonymous Symbol]
    oled.o(.text.OLED_Clear) refers to oled.o(.text.IIC_XIE_ML) for IIC_XIE_ML
    oled.o(.text.OLED_Clear) refers to led_key.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.text.OLED_Clear) refers to oled.o(.text.IIC_XIE) for IIC_XIE
    oled.o(.ARM.exidx.text.OLED_Clear) refers to oled.o(.text.OLED_Clear) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowChar) refers to oled.o(.text.IIC_XIE_ML) for IIC_XIE_ML
    oled.o(.text.OLED_ShowChar) refers to oled.o(.text.IIC_XIE_DATA) for IIC_XIE_DATA
    oled.o(.text.OLED_ShowChar) refers to oled.o(.rodata.F6x8) for F6x8
    oled.o(.text.OLED_ShowChar) refers to oled.o(.rodata.F8X16) for F8X16
    oled.o(.ARM.exidx.text.OLED_ShowChar) refers to oled.o(.text.OLED_ShowChar) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowString) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowChinese) refers to oled.o(.text.IIC_XIE_ML) for IIC_XIE_ML
    oled.o(.text.OLED_ShowChinese) refers to oled.o(.text.IIC_XIE_DATA) for IIC_XIE_DATA
    oled.o(.text.OLED_ShowChinese) refers to oled.o(.data.Hzk2) for Hzk2
    oled.o(.ARM.exidx.text.OLED_ShowChinese) refers to oled.o(.text.OLED_ShowChinese) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawBMP) refers to oled.o(.text.IIC_XIE_ML) for IIC_XIE_ML
    oled.o(.text.OLED_DrawBMP) refers to led_key.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.text.OLED_DrawBMP) refers to oled.o(.text.IIC_XIE) for IIC_XIE
    oled.o(.ARM.exidx.text.OLED_DrawBMP) refers to oled.o(.text.OLED_DrawBMP) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.OLED_IIC_Init) refers to oled.o(.text.OLED_IIC_Init) for [Anonymous Symbol]
    oled.o(.text.OLED_Init) refers to oled.o(.text.IIC_XIE_ML) for IIC_XIE_ML
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_Clear) for OLED_Clear
    oled.o(.ARM.exidx.text.OLED_Init) refers to oled.o(.text.OLED_Init) for [Anonymous Symbol]
    oled.o(.text.OLED_Write) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    oled.o(.text.OLED_Write) refers to printfa.o(i.__0vsprintf) for vsprintf
    oled.o(.text.OLED_Write) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.text.OLED_Write) refers to oled.o(.data.OLED_Lock) for OLED_Lock
    oled.o(.ARM.exidx.text.OLED_Write) refers to oled.o(.text.OLED_Write) for [Anonymous Symbol]
    motor.o(.text.Set_Motor) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor.o(.ARM.exidx.text.Set_Motor) refers to motor.o(.text.Set_Motor) for [Anonymous Symbol]
    motor.o(.text.Motor_Write) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor.o(.ARM.exidx.text.Motor_Write) refers to motor.o(.text.Motor_Write) for [Anonymous Symbol]
    led_key.o(.ARM.exidx.text.GPIO_WriteBit) refers to led_key.o(.text.GPIO_WriteBit) for [Anonymous Symbol]
    enconder.o(.text.Get_Encoder) refers to enconder.o(.data.dir) for dir
    enconder.o(.ARM.exidx.text.Get_Encoder) refers to enconder.o(.text.Get_Encoder) for [Anonymous Symbol]
    enconder.o(.ARM.exidx.text.Encoder_init) refers to enconder.o(.text.Encoder_init) for [Anonymous Symbol]
    delay.o(.ARM.exidx.text.delay_noOS) refers to delay.o(.text.delay_noOS) for [Anonymous Symbol]
    control.o(.text.PID_Init) refers to control.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    control.o(.ARM.exidx.text.PID_Init) refers to control.o(.text.PID_Init) for [Anonymous Symbol]
    control.o(.text.Set_PID_1) refers to control.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    control.o(.text.Set_PID_1) refers to startuptask.o(.data..L_MergedGlobals) for Greeny
    control.o(.ARM.exidx.text.Set_PID_1) refers to control.o(.text.Set_PID_1) for [Anonymous Symbol]
    control.o(.text.Set_PID_2) refers to control.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    control.o(.text.Set_PID_2) refers to startuptask.o(.data..L_MergedGlobals) for Greeny
    control.o(.ARM.exidx.text.Set_PID_2) refers to control.o(.text.Set_PID_2) for [Anonymous Symbol]
    control.o(.text.Erect_pid) refers to fadd.o(.text) for __aeabi_fsub
    control.o(.text.Erect_pid) refers to fmul.o(.text) for __aeabi_fmul
    control.o(.ARM.exidx.text.Erect_pid) refers to control.o(.text.Erect_pid) for [Anonymous Symbol]
    control.o(.text.Get_hw) refers to control.o(.bss.Line) for Line
    control.o(.ARM.exidx.text.Get_hw) refers to control.o(.text.Get_hw) for [Anonymous Symbol]
    control.o(.text.Line_Control) refers to motor.o(.text.Motor_Write) for Motor_Write
    control.o(.text.Line_Control) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    control.o(.text.Line_Control) refers to control.o(.bss.Line) for Line
    control.o(.ARM.exidx.text.Line_Control) refers to control.o(.text.Line_Control) for [Anonymous Symbol]
    control.o(.text.Motor_Open) refers to control.o(.text.Line_Control) for Line_Control
    control.o(.text.Motor_Open) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    control.o(.ARM.exidx.text.Motor_Open) refers to control.o(.text.Motor_Open) for [Anonymous Symbol]
    control.o(.text.TASK_1) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    control.o(.text.TASK_1) refers to oled.o(.text.OLED_Clear) for OLED_Clear
    control.o(.text.TASK_1) refers to oled.o(.text.OLED_Write) for OLED_Write
    control.o(.text.TASK_1) refers to startuptask.o(.data.car_go_dir) for car_go_dir
    control.o(.text.TASK_1) refers to control.o(.rodata.str1.1) for [Anonymous Symbol]
    control.o(.text.TASK_1) refers to startuptask.o(.bss..L_MergedGlobals.7) for Line_EN
    control.o(.ARM.exidx.text.TASK_1) refers to control.o(.text.TASK_1) for [Anonymous Symbol]
    control.o(.text.TASK_3) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    control.o(.text.TASK_3) refers to oled.o(.text.OLED_Clear) for OLED_Clear
    control.o(.text.TASK_3) refers to oled.o(.text.OLED_Write) for OLED_Write
    control.o(.text.TASK_3) refers to startuptask.o(.bss..L_MergedGlobals.7) for YunTai_EN
    control.o(.text.TASK_3) refers to startuptask.o(.data..L_MergedGlobals) for yutai_3_init_falg
    control.o(.ARM.exidx.text.TASK_3) refers to control.o(.text.TASK_3) for [Anonymous Symbol]
    control.o(.text.TASK_4) refers to startuptask.o(.bss..L_MergedGlobals.7) for YunTai_EN
    control.o(.ARM.exidx.text.TASK_4) refers to control.o(.text.TASK_4) for [Anonymous Symbol]
    control.o(.text.TI) refers to oled.o(.text.OLED_Write) for OLED_Write
    control.o(.text.TI) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    control.o(.text.TI) refers to oled.o(.text.OLED_Clear) for OLED_Clear
    control.o(.text.TI) refers to control.o(.text.TASK_1) for TASK_1
    control.o(.text.TI) refers to control.o(.text.TASK_3) for TASK_3
    control.o(.text.TI) refers to control.o(.rodata.str1.1) for [Anonymous Symbol]
    control.o(.text.TI) refers to control.o(.bss.a) for a
    control.o(.text.TI) refers to startuptask.o(.bss..L_MergedGlobals.7) for YunTai_EN
    control.o(.ARM.exidx.text.TI) refers to control.o(.text.TI) for [Anonymous Symbol]
    maixcam.o(.ARM.exidx.text.UART2_Init) refers to maixcam.o(.text.UART2_Init) for [Anonymous Symbol]
    maixcam.o(.ARM.exidx.text.uart2_send_char) refers to maixcam.o(.text.uart2_send_char) for [Anonymous Symbol]
    maixcam.o(.ARM.exidx.text.uart2_send_string) refers to maixcam.o(.text.uart2_send_string) for [Anonymous Symbol]
    maixcam.o(.ARM.exidx.text.fputc) refers to maixcam.o(.text.fputc) for [Anonymous Symbol]
    maixcam.o(.ARM.exidx.text.MaixCam_Init) refers to maixcam.o(.text.MaixCam_Init) for [Anonymous Symbol]
    maixcam.o(.ARM.exidx.text.MaixCam_Send_XY) refers to maixcam.o(.text.MaixCam_Send_XY) for [Anonymous Symbol]
    maixcam.o(.text.UART3_IRQHandler) refers to maixcam.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    maixcam.o(.ARM.exidx.text.UART3_IRQHandler) refers to maixcam.o(.text.UART3_IRQHandler) for [Anonymous Symbol]
    yunque.o(.ARM.exidx.text.UART3_Init) refers to yunque.o(.text.UART3_Init) for [Anonymous Symbol]
    yunque.o(.ARM.exidx.text.uart3_send_char) refers to yunque.o(.text.uart3_send_char) for [Anonymous Symbol]
    yunque.o(.ARM.exidx.text.uart3_send_string) refers to yunque.o(.text.uart3_send_string) for [Anonymous Symbol]
    yunque.o(.ARM.exidx.text.YunQue_Init) refers to yunque.o(.text.YunQue_Init) for [Anonymous Symbol]
    yunque.o(.text.YunQue_CMD) refers to printfa.o(i.__0vsprintf) for vsprintf
    yunque.o(.text.YunQue_CMD) refers to tasks.o(.text.vTaskDelay) for vTaskDelay
    yunque.o(.ARM.exidx.text.YunQue_CMD) refers to yunque.o(.text.YunQue_CMD) for [Anonymous Symbol]
    emm_v5.o(.text.Speed_Control) refers to fcmpge.o(.text) for __aeabi_fcmpge
    emm_v5.o(.text.Speed_Control) refers to ffixi.o(.text) for __aeabi_f2iz
    emm_v5.o(.text.Speed_Control) refers to bsp_uart.o(.text.Uart_Send) for Uart_Send
    emm_v5.o(.text.Speed_Control) refers to fcmplt.o(.text) for __aeabi_fcmplt
    emm_v5.o(.ARM.exidx.text.Speed_Control) refers to emm_v5.o(.text.Speed_Control) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Vel_Control) refers to bsp_uart.o(.text.Uart_Send) for Uart_Send
    emm_v5.o(.ARM.exidx.text.Emm_V5_Vel_Control) refers to emm_v5.o(.text.Emm_V5_Vel_Control) for [Anonymous Symbol]
    emm_v5.o(.text.POS_Control) refers to fcmpge.o(.text) for __aeabi_fcmpge
    emm_v5.o(.text.POS_Control) refers to ffixui.o(.text) for __aeabi_f2uiz
    emm_v5.o(.text.POS_Control) refers to ffixi.o(.text) for __aeabi_f2iz
    emm_v5.o(.text.POS_Control) refers to bsp_uart.o(.text.Uart_Send) for Uart_Send
    emm_v5.o(.text.POS_Control) refers to fcmplt.o(.text) for __aeabi_fcmplt
    emm_v5.o(.ARM.exidx.text.POS_Control) refers to emm_v5.o(.text.POS_Control) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Pos_Control) refers to bsp_uart.o(.text.Uart_Send) for Uart_Send
    emm_v5.o(.ARM.exidx.text.Emm_V5_Pos_Control) refers to emm_v5.o(.text.Emm_V5_Pos_Control) for [Anonymous Symbol]
    emm_v5.o(.text.usart_SendCmd) refers to bsp_uart.o(.text.Uart_Send) for Uart_Send
    emm_v5.o(.ARM.exidx.text.usart_SendCmd) refers to emm_v5.o(.text.usart_SendCmd) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Reset_CurPos_To_Zero) refers to bsp_uart.o(.text.Uart_Send) for Uart_Send
    emm_v5.o(.ARM.exidx.text.Emm_V5_Reset_CurPos_To_Zero) refers to emm_v5.o(.text.Emm_V5_Reset_CurPos_To_Zero) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Reset_Clog_Pro) refers to bsp_uart.o(.text.Uart_Send) for Uart_Send
    emm_v5.o(.ARM.exidx.text.Emm_V5_Reset_Clog_Pro) refers to emm_v5.o(.text.Emm_V5_Reset_Clog_Pro) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Read_Sys_Params) refers to memseta.o(.text) for __aeabi_memclr
    emm_v5.o(.text.Emm_V5_Read_Sys_Params) refers to bsp_uart.o(.text.Uart_Send) for Uart_Send
    emm_v5.o(.ARM.exidx.text.Emm_V5_Read_Sys_Params) refers to emm_v5.o(.text.Emm_V5_Read_Sys_Params) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Modify_Ctrl_Mode) refers to memseta.o(.text) for __aeabi_memclr
    emm_v5.o(.text.Emm_V5_Modify_Ctrl_Mode) refers to bsp_uart.o(.text.Uart_Send) for Uart_Send
    emm_v5.o(.ARM.exidx.text.Emm_V5_Modify_Ctrl_Mode) refers to emm_v5.o(.text.Emm_V5_Modify_Ctrl_Mode) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_En_Control) refers to memseta.o(.text) for __aeabi_memclr
    emm_v5.o(.text.Emm_V5_En_Control) refers to bsp_uart.o(.text.Uart_Send) for Uart_Send
    emm_v5.o(.ARM.exidx.text.Emm_V5_En_Control) refers to emm_v5.o(.text.Emm_V5_En_Control) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Stop_Now) refers to memseta.o(.text) for __aeabi_memclr
    emm_v5.o(.text.Emm_V5_Stop_Now) refers to bsp_uart.o(.text.Uart_Send) for Uart_Send
    emm_v5.o(.ARM.exidx.text.Emm_V5_Stop_Now) refers to emm_v5.o(.text.Emm_V5_Stop_Now) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Synchronous_motion) refers to bsp_uart.o(.text.Uart_Send) for Uart_Send
    emm_v5.o(.ARM.exidx.text.Emm_V5_Synchronous_motion) refers to emm_v5.o(.text.Emm_V5_Synchronous_motion) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Origin_Set_O) refers to memseta.o(.text) for __aeabi_memclr
    emm_v5.o(.text.Emm_V5_Origin_Set_O) refers to bsp_uart.o(.text.Uart_Send) for Uart_Send
    emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Set_O) refers to emm_v5.o(.text.Emm_V5_Origin_Set_O) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Origin_Modify_Params) refers to bsp_uart.o(.text.Uart_Send) for Uart_Send
    emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Modify_Params) refers to emm_v5.o(.text.Emm_V5_Origin_Modify_Params) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Origin_Trigger_Return) refers to memseta.o(.text) for __aeabi_memclr
    emm_v5.o(.text.Emm_V5_Origin_Trigger_Return) refers to bsp_uart.o(.text.Uart_Send) for Uart_Send
    emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Trigger_Return) refers to emm_v5.o(.text.Emm_V5_Origin_Trigger_Return) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Origin_Interrupt) refers to bsp_uart.o(.text.Uart_Send) for Uart_Send
    emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Interrupt) refers to emm_v5.o(.text.Emm_V5_Origin_Interrupt) for [Anonymous Symbol]
    emm_v5.o(.text.Wait_answer) refers to emm_v5.o(.bss.rxFrameFlag) for rxFrameFlag
    emm_v5.o(.ARM.exidx.text.Wait_answer) refers to emm_v5.o(.text.Wait_answer) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Data_Dispose) refers to memcpya.o(.text) for __aeabi_memcpy
    emm_v5.o(.text.Emm_V5_Data_Dispose) refers to emm_v5.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    emm_v5.o(.ARM.exidx.text.Emm_V5_Data_Dispose) refers to emm_v5.o(.text.Emm_V5_Data_Dispose) for [Anonymous Symbol]
    bsp_uart.o(.text.Uart_Send) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for DL_UART_transmitDataBlocking
    bsp_uart.o(.ARM.exidx.text.Uart_Send) refers to bsp_uart.o(.text.Uart_Send) for [Anonymous Symbol]
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to maixcam.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to maixcam.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to maixcam.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to maixcam.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    fcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmplt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpge.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpgt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    idiv_div0.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    depilogue.o(i.__ARM_clz) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing dl_adc12.o(.text), (0 bytes).
    Removing dl_adc12.o(.text.DL_ADC12_setClockConfig), (64 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_setClockConfig), (8 bytes).
    Removing dl_adc12.o(.text.DL_ADC12_getClockConfig), (40 bytes).
    Removing dl_adc12.o(.ARM.exidx.text.DL_ADC12_getClockConfig), (8 bytes).
    Removing dl_aes.o(.text), (0 bytes).
    Removing dl_aes.o(.text.DL_AES_setKey), (72 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_setKey), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_setKeyAligned), (56 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_setKeyAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataIn), (40 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataIn), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadDataInAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadDataInAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_getDataOut), (36 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_getDataOut), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_getDataOutAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_getDataOutAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataIn), (40 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataIn), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInWithoutTrigger), (40 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTrigger), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_loadXORDataInWithoutTriggerAligned), (24 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_loadXORDataInWithoutTriggerAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_xorData), (52 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_xorData), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_xorDataAligned), (36 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_xorDataAligned), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_saveConfiguration), (64 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_saveConfiguration), (8 bytes).
    Removing dl_aes.o(.text.DL_AES_restoreConfiguration), (60 bytes).
    Removing dl_aes.o(.ARM.exidx.text.DL_AES_restoreConfiguration), (8 bytes).
    Removing dl_aesadv.o(.text), (0 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_crc.o(.text), (0 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateBlock32), (96 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateMemoryRange32), (52 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange32), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateBlock16), (156 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateBlock16), (8 bytes).
    Removing dl_crc.o(.text.DL_CRC_calculateMemoryRange16), (104 bytes).
    Removing dl_crc.o(.ARM.exidx.text.DL_CRC_calculateMemoryRange16), (8 bytes).
    Removing dl_crcp.o(.text), (0 bytes).
    Removing dl_dac12.o(.text), (0 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_init), (136 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_init), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_outputBlocking8), (48 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_outputBlocking12), (56 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_outputBlocking12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_fillFIFO8), (132 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO8), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_fillFIFO12), (56 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_fillFIFO12), (8 bytes).
    Removing dl_dac12.o(.text.DL_DAC12_performSelfCalibrationBlocking), (36 bytes).
    Removing dl_dac12.o(.ARM.exidx.text.DL_DAC12_performSelfCalibrationBlocking), (8 bytes).
    Removing dl_dma.o(.text), (0 bytes).
    Removing dl_dma.o(.text.DL_DMA_initChannel), (68 bytes).
    Removing dl_dma.o(.ARM.exidx.text.DL_DMA_initChannel), (8 bytes).
    Removing dl_flashctl.o(.text), (0 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseMemory), (22 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseMemoryFromRAM), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseMemoryFromRAM), (8 bytes).
    Removing dl_flashctl.o(.ramfunc), (52 bytes).
    Removing dl_flashctl.o(.ARM.exidx.ramfunc), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massErase), (124 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massErase), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectMainMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectNonMainMemory), (16 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectNonMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseDataBank), (332 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massEraseFromRAM), (88 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_eraseDataBankFromRAM), (264 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_eraseDataBankFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_massEraseMultiBank), (544 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_massEraseMultiBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryReset), (192 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryReset), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectNonMainMemory), (12 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectNonMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryResetFromRAM), (116 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_factoryResetMultiBank), (92 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_factoryResetMultiBank), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16), (26 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32), (26 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64), (30 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCGenerated), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCGenerated), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCGenerated), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory8WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory16WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory32WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemory64WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemory64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated), (144 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectSector), (224 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectSector), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated), (104 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking64WithECCManual), (176 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual), (132 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlockingFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryBlocking), (168 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryBlocking), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_programMemoryFromRAM), (120 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_programMemoryFromRAM), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectDataMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectDataMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectMainMemory), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectMainMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_unprotectAllMemory), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_unprotectAllMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectAllMemory), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectAllMemory), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_protectSector), (240 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_protectSector), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8), (26 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16), (26 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16), (28 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated), (32 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual), (44 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerifyFromRAM64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCGenerated), (30 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCGenerated), (30 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCGenerated), (30 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCGenerated), (36 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCGenerated), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify8WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify8WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify16WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify16WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify32WithECCManual), (40 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify32WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_readVerify64WithECCManual), (48 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_readVerify64WithECCManual), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_blankVerify), (20 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerify), (8 bytes).
    Removing dl_flashctl.o(.text.DL_FlashCTL_blankVerifyFromRAM), (24 bytes).
    Removing dl_flashctl.o(.ARM.exidx.text.DL_FlashCTL_blankVerifyFromRAM), (8 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.text.DL_I2C_setClockConfig), (38 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (26 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO), (152 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO), (68 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (52 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (156 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (68 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (52 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (36 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (28 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (24 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_keystorectl.o(.text), (0 bytes).
    Removing dl_lcd.o(.text), (0 bytes).
    Removing dl_lfss.o(.text), (0 bytes).
    Removing dl_mathacl.o(.text), (0 bytes).
    Removing dl_mathacl.o(.text.DL_MathACL_configOperation), (40 bytes).
    Removing dl_mathacl.o(.ARM.exidx.text.DL_MathACL_configOperation), (8 bytes).
    Removing dl_mcan.o(.text), (0 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isReady), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isReady), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setClockConfig), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockConfig), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isInReset), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isInReset), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isFDOpEnable), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isFDOpEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_isMemInitDone), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_isMemInitDone), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setOpMode), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setOpMode), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getOpMode), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getOpMode), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_init), (216 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_init), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_config), (212 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_config), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccConfig), (184 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setBitTime), (216 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_msgRAMConfig), (520 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_msgRAMConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setExtIDAndMask), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setExtIDAndMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeMsgRam), (264 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufAddReq), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufAddReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getNewDataStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearNewDataStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearNewDataStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readMsgRam), (292 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readMsgRam), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_readTxEventFIFO), (96 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_readTxEventFIFO), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addStdMsgIDFilter), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addStdMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addExtMsgIDFilter), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addExtMsgIDFilter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_lpbkModeEnable), (96 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_lpbkModeEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getErrCounters), (40 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getErrCounters), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getProtocolStatus), (80 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getProtocolStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_enableIntr), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_selectIntrLine), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_selectIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrLineSelectStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrLineSelectStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_enableIntrLine), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_enableIntrLine), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getIntrStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_clearIntrStatus), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_clearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getHighPriorityMsgStatus), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getHighPriorityMsgStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxFIFOStatus), (60 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeRxFIFOAck), (64 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeRxFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxFIFOQueStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxFIFOQueStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufReqPend), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufReqPend), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationReq), (52 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationReq), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufTransmissionStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufTransmissionStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_txBufCancellationStatus), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_txBufCancellationStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_TXBufTransIntrEnable), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_TXBufTransIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxBufCancellationIntrEnable), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxBufCancellationIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxEventFIFOStatus), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxEventFIFOStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_addClockStopRequest), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_addClockStopRequest), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_writeTxEventFIFOAck), (44 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_writeTxEventFIFOAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccForceError), (220 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccForceError), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetErrorStatus), (128 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearErrorStatus), (112 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearErrorStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWriteEOI), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccEnableIntr), (56 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccGetIntrStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccGetIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccClearIntrStatus), (36 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccClearIntrStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterConfig), (24 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterConfig), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSCounterEnable), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSCounterEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSEnableIntr), (28 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSEnableIntr), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSWriteEOI), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSWriteEOI), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSGetUnservicedIntrCount), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSGetUnservicedIntrCount), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRevisionId), (68 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClockStopAck), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClockStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSSetRawStatus), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSSetRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSClearRawStatus), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSClearRawStatus), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getRxPinState), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getRxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_setTxPinState), (48 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_setTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTxPinState), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTxPinState), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTSCounterVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTSCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getClkStopAck), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getClkStopAck), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getBitTime), (76 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getBitTime), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_resetTSCounter), (20 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_resetTSCounter), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getTOCounterVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getTOCounterVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccAggrGetRevisionId), (32 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccAggrGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_eccWrapGetRevisionId), (84 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_eccWrapGetRevisionId), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_extTSIsIntrEnable), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_extTSIsIntrEnable), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getEndianVal), (12 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getEndianVal), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_getExtIDANDMask), (16 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_getExtIDANDMask), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_saveConfiguration), (236 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_saveConfiguration), (8 bytes).
    Removing dl_mcan.o(.text.DL_MCAN_restoreConfiguration), (276 bytes).
    Removing dl_mcan.o(.ARM.exidx.text.DL_MCAN_restoreConfiguration), (8 bytes).
    Removing dl_mcan.o(.rodata..L__const.DL_MCAN_getDataSize.dataSize), (64 bytes).
    Removing dl_mcan.o(.rodata.cst32), (32 bytes).
    Removing dl_opa.o(.text), (0 bytes).
    Removing dl_opa.o(.text.DL_OPA_increaseGain), (52 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_increaseGain), (8 bytes).
    Removing dl_opa.o(.text.DL_OPA_decreaseGain), (48 bytes).
    Removing dl_opa.o(.ARM.exidx.text.DL_OPA_decreaseGain), (8 bytes).
    Removing dl_rtc_common.o(.text), (0 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_initCalendar), (144 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_initCalendar), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarTime), (124 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarTime), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm1), (96 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm1), (84 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm1), (60 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm1), (68 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm1), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_setCalendarAlarm2), (96 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_setCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_getCalendarAlarm2), (84 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_getCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_enableCalendarAlarm2), (60 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_enableCalendarAlarm2), (8 bytes).
    Removing dl_rtc_common.o(.text.DL_RTC_Common_disableCalendarAlarm2), (68 bytes).
    Removing dl_rtc_common.o(.ARM.exidx.text.DL_RTC_Common_disableCalendarAlarm2), (8 bytes).
    Removing dl_spi.o(.text), (0 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_init), (8 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_setClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_getClockConfig), (16 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_getClockConfig), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking8), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking16), (40 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataBlocking32), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking8), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking16), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataBlocking32), (36 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataBlocking32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck8), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck16), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_receiveDataCheck32), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_receiveDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck8), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck16), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_transmitDataCheck32), (28 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_transmitDataCheck32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO8), (100 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO16), (104 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_drainRXFIFO32), (104 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_drainRXFIFO32), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO8), (100 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO8), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO16), (104 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO16), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_saveConfiguration), (88 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_saveConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_restoreConfiguration), (112 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_restoreConfiguration), (8 bytes).
    Removing dl_spi.o(.text.DL_SPI_fillTXFIFO32), (104 bytes).
    Removing dl_spi.o(.ARM.exidx.text.DL_SPI_fillTXFIFO32), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (268 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (104 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (224 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (160 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (96 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (200 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (196 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_initPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (272 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_trng.o(.text), (0 bytes).
    Removing dl_trng.o(.text.DL_TRNG_saveConfiguration), (52 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_saveConfiguration), (8 bytes).
    Removing dl_trng.o(.text.DL_TRNG_restoreConfiguration), (72 bytes).
    Removing dl_trng.o(.ARM.exidx.text.DL_TRNG_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (68 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (48 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (100 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (100 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (96 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (112 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_vref.o(.text), (0 bytes).
    Removing dl_vref.o(.text.DL_VREF_configReference), (32 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_configReference), (8 bytes).
    Removing dl_vref.o(.text.DL_VREF_setClockConfig), (18 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_setClockConfig), (8 bytes).
    Removing dl_vref.o(.text.DL_VREF_getClockConfig), (16 bytes).
    Removing dl_vref.o(.ARM.exidx.text.DL_VREF_getClockConfig), (8 bytes).
    Removing dl_interrupt.o(.text), (0 bytes).
    Removing dl_interrupt.o(.text.DL_Interrupt_registerInterrupt), (284 bytes).
    Removing dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_registerInterrupt), (8 bytes).
    Removing dl_interrupt.o(.text.DL_Interrupt_unregisterInterrupt), (20 bytes).
    Removing dl_interrupt.o(.ARM.exidx.text.DL_Interrupt_unregisterInterrupt), (8 bytes).
    Removing dl_interrupt.o(.vtable), (192 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text), (0 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL), (240 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT), (100 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (60 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT), (96 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (108 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC), (28 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP), (52 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY), (8 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MOTOR_PWM_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_Servo_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_QEI_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_TIMER_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_3_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SPI_WS2812_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (100 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (108 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing startup_mspm0g350x_uvision.o(HEAP), (512 bytes).
    Removing croutine.o(.text), (0 bytes).
    Removing event_groups.o(.text), (0 bytes).
    Removing event_groups.o(.text.xEventGroupCreate), (28 bytes).
    Removing event_groups.o(.ARM.exidx.text.xEventGroupCreate), (8 bytes).
    Removing event_groups.o(.text.xEventGroupSync), (316 bytes).
    Removing event_groups.o(.ARM.exidx.text.xEventGroupSync), (8 bytes).
    Removing event_groups.o(.text.xEventGroupSetBits), (140 bytes).
    Removing event_groups.o(.ARM.exidx.text.xEventGroupSetBits), (8 bytes).
    Removing event_groups.o(.text.xEventGroupWaitBits), (236 bytes).
    Removing event_groups.o(.ARM.exidx.text.xEventGroupWaitBits), (8 bytes).
    Removing event_groups.o(.text.xEventGroupClearBits), (42 bytes).
    Removing event_groups.o(.ARM.exidx.text.xEventGroupClearBits), (8 bytes).
    Removing event_groups.o(.text.xEventGroupClearBitsFromISR), (20 bytes).
    Removing event_groups.o(.ARM.exidx.text.xEventGroupClearBitsFromISR), (8 bytes).
    Removing event_groups.o(.text.vEventGroupClearBitsCallback), (38 bytes).
    Removing event_groups.o(.ARM.exidx.text.vEventGroupClearBitsCallback), (8 bytes).
    Removing event_groups.o(.text.xEventGroupGetBitsFromISR), (18 bytes).
    Removing event_groups.o(.ARM.exidx.text.xEventGroupGetBitsFromISR), (8 bytes).
    Removing event_groups.o(.text.vEventGroupDelete), (62 bytes).
    Removing event_groups.o(.ARM.exidx.text.vEventGroupDelete), (8 bytes).
    Removing event_groups.o(.text.vEventGroupSetBitsCallback), (140 bytes).
    Removing event_groups.o(.ARM.exidx.text.vEventGroupSetBitsCallback), (8 bytes).
    Removing event_groups.o(.text.xEventGroupSetBitsFromISR), (20 bytes).
    Removing event_groups.o(.ARM.exidx.text.xEventGroupSetBitsFromISR), (8 bytes).
    Removing event_groups.o(.text.uxEventGroupGetNumber), (12 bytes).
    Removing event_groups.o(.ARM.exidx.text.uxEventGroupGetNumber), (8 bytes).
    Removing event_groups.o(.text.vEventGroupSetNumber), (4 bytes).
    Removing event_groups.o(.ARM.exidx.text.vEventGroupSetNumber), (8 bytes).
    Removing list.o(.text), (0 bytes).
    Removing list.o(.ARM.exidx.text.vListInitialise), (8 bytes).
    Removing list.o(.ARM.exidx.text.vListInitialiseItem), (8 bytes).
    Removing list.o(.text.vListInsertEnd), (24 bytes).
    Removing list.o(.ARM.exidx.text.vListInsertEnd), (8 bytes).
    Removing list.o(.ARM.exidx.text.vListInsert), (8 bytes).
    Removing list.o(.ARM.exidx.text.uxListRemove), (8 bytes).
    Removing queue.o(.text), (0 bytes).
    Removing queue.o(.text.xQueueGenericReset), (140 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueGenericReset), (8 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueGenericCreate), (8 bytes).
    Removing queue.o(.text.xQueueCreateMutex), (112 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueCreateMutex), (8 bytes).
    Removing queue.o(.text.xQueueGetMutexHolder), (42 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueGetMutexHolder), (8 bytes).
    Removing queue.o(.text.xQueueGetMutexHolderFromISR), (22 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueGetMutexHolderFromISR), (8 bytes).
    Removing queue.o(.text.xQueueGiveMutexRecursive), (58 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueGiveMutexRecursive), (8 bytes).
    Removing queue.o(.text.xQueueGenericSend), (416 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueGenericSend), (8 bytes).
    Removing queue.o(.text.xQueueTakeMutexRecursive), (58 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueTakeMutexRecursive), (8 bytes).
    Removing queue.o(.text.xQueueSemaphoreTake), (360 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueSemaphoreTake), (8 bytes).
    Removing queue.o(.text.xQueueCreateCountingSemaphore), (32 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueCreateCountingSemaphore), (8 bytes).
    Removing queue.o(.ARM.exidx.text.prvUnlockQueue), (8 bytes).
    Removing queue.o(.text.xQueueGenericSendFromISR), (300 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueGenericSendFromISR), (8 bytes).
    Removing queue.o(.text.xQueueGiveFromISR), (174 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueGiveFromISR), (8 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueReceive), (8 bytes).
    Removing queue.o(.text.xQueuePeek), (320 bytes).
    Removing queue.o(.ARM.exidx.text.xQueuePeek), (8 bytes).
    Removing queue.o(.text.xQueueReceiveFromISR), (182 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueReceiveFromISR), (8 bytes).
    Removing queue.o(.text.xQueuePeekFromISR), (98 bytes).
    Removing queue.o(.ARM.exidx.text.xQueuePeekFromISR), (8 bytes).
    Removing queue.o(.text.uxQueueMessagesWaiting), (26 bytes).
    Removing queue.o(.ARM.exidx.text.uxQueueMessagesWaiting), (8 bytes).
    Removing queue.o(.text.uxQueueSpacesAvailable), (30 bytes).
    Removing queue.o(.ARM.exidx.text.uxQueueSpacesAvailable), (8 bytes).
    Removing queue.o(.text.uxQueueMessagesWaitingFromISR), (12 bytes).
    Removing queue.o(.ARM.exidx.text.uxQueueMessagesWaitingFromISR), (8 bytes).
    Removing queue.o(.text.vQueueDelete), (140 bytes).
    Removing queue.o(.ARM.exidx.text.vQueueDelete), (8 bytes).
    Removing queue.o(.text.vQueueUnregisterQueue), (132 bytes).
    Removing queue.o(.ARM.exidx.text.vQueueUnregisterQueue), (8 bytes).
    Removing queue.o(.text.uxQueueGetQueueNumber), (4 bytes).
    Removing queue.o(.ARM.exidx.text.uxQueueGetQueueNumber), (8 bytes).
    Removing queue.o(.text.vQueueSetQueueNumber), (4 bytes).
    Removing queue.o(.ARM.exidx.text.vQueueSetQueueNumber), (8 bytes).
    Removing queue.o(.text.ucQueueGetQueueType), (6 bytes).
    Removing queue.o(.ARM.exidx.text.ucQueueGetQueueType), (8 bytes).
    Removing queue.o(.text.xQueueIsQueueEmptyFromISR), (16 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueIsQueueEmptyFromISR), (8 bytes).
    Removing queue.o(.text.xQueueIsQueueFullFromISR), (20 bytes).
    Removing queue.o(.ARM.exidx.text.xQueueIsQueueFullFromISR), (8 bytes).
    Removing queue.o(.ARM.exidx.text.vQueueAddToRegistry), (8 bytes).
    Removing queue.o(.text.pcQueueGetName), (132 bytes).
    Removing queue.o(.ARM.exidx.text.pcQueueGetName), (8 bytes).
    Removing queue.o(.ARM.exidx.text.vQueueWaitForMessageRestricted), (8 bytes).
    Removing tasks.o(.text), (0 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskCreate), (8 bytes).
    Removing tasks.o(.text.vTaskDelete), (176 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskDelete), (8 bytes).
    Removing tasks.o(.text.xTaskDelayUntil), (164 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskDelayUntil), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskSuspendAll), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskResumeAll), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskDelay), (8 bytes).
    Removing tasks.o(.text.eTaskGetState), (136 bytes).
    Removing tasks.o(.ARM.exidx.text.eTaskGetState), (8 bytes).
    Removing tasks.o(.text.uxTaskPriorityGet), (40 bytes).
    Removing tasks.o(.ARM.exidx.text.uxTaskPriorityGet), (8 bytes).
    Removing tasks.o(.text.uxTaskPriorityGetFromISR), (40 bytes).
    Removing tasks.o(.ARM.exidx.text.uxTaskPriorityGetFromISR), (8 bytes).
    Removing tasks.o(.text.vTaskPrioritySet), (212 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskPrioritySet), (8 bytes).
    Removing tasks.o(.text.vTaskSuspend), (356 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskSuspend), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskSwitchContext), (8 bytes).
    Removing tasks.o(.text.vTaskResume), (124 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskResume), (8 bytes).
    Removing tasks.o(.text.xTaskResumeFromISR), (160 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskResumeFromISR), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskStartScheduler), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.prvIdleTask), (8 bytes).
    Removing tasks.o(.text.vTaskEndScheduler), (20 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskEndScheduler), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskIncrementTick), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskGetTickCount), (8 bytes).
    Removing tasks.o(.text.xTaskGetTickCountFromISR), (12 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskGetTickCountFromISR), (8 bytes).
    Removing tasks.o(.text.uxTaskGetNumberOfTasks), (12 bytes).
    Removing tasks.o(.ARM.exidx.text.uxTaskGetNumberOfTasks), (8 bytes).
    Removing tasks.o(.text.pcTaskGetName), (24 bytes).
    Removing tasks.o(.ARM.exidx.text.pcTaskGetName), (8 bytes).
    Removing tasks.o(.text.uxTaskGetSystemState), (1132 bytes).
    Removing tasks.o(.ARM.exidx.text.uxTaskGetSystemState), (8 bytes).
    Removing tasks.o(.text.xTaskCatchUpTicks), (48 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskCatchUpTicks), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskPlaceOnEventList), (8 bytes).
    Removing tasks.o(.text.vTaskPlaceOnUnorderedEventList), (196 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskPlaceOnUnorderedEventList), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskPlaceOnEventListRestricted), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskRemoveFromEventList), (8 bytes).
    Removing tasks.o(.text.vTaskRemoveFromUnorderedEventList), (160 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskRemoveFromUnorderedEventList), (8 bytes).
    Removing tasks.o(.text.vTaskSetTimeOutState), (36 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskSetTimeOutState), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskInternalSetTimeOutState), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskCheckForTimeOut), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskMissedYield), (8 bytes).
    Removing tasks.o(.text.uxTaskGetTaskNumber), (12 bytes).
    Removing tasks.o(.ARM.exidx.text.uxTaskGetTaskNumber), (8 bytes).
    Removing tasks.o(.text.vTaskSetTaskNumber), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskSetTaskNumber), (8 bytes).
    Removing tasks.o(.text.vTaskGetInfo), (304 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskGetInfo), (8 bytes).
    Removing tasks.o(.text.uxTaskGetStackHighWaterMark), (88 bytes).
    Removing tasks.o(.ARM.exidx.text.uxTaskGetStackHighWaterMark), (8 bytes).
    Removing tasks.o(.text.xTaskGetCurrentTaskHandle), (12 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskGetCurrentTaskHandle), (8 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskGetSchedulerState), (8 bytes).
    Removing tasks.o(.text.xTaskPriorityInherit), (156 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskPriorityInherit), (8 bytes).
    Removing tasks.o(.text.xTaskPriorityDisinherit), (124 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskPriorityDisinherit), (8 bytes).
    Removing tasks.o(.text.vTaskPriorityDisinheritAfterTimeout), (144 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskPriorityDisinheritAfterTimeout), (8 bytes).
    Removing tasks.o(.text.uxTaskResetEventItemValue), (24 bytes).
    Removing tasks.o(.ARM.exidx.text.uxTaskResetEventItemValue), (8 bytes).
    Removing tasks.o(.text.pvTaskIncrementMutexHeldCount), (24 bytes).
    Removing tasks.o(.ARM.exidx.text.pvTaskIncrementMutexHeldCount), (8 bytes).
    Removing tasks.o(.text.ulTaskGenericNotifyTake), (224 bytes).
    Removing tasks.o(.ARM.exidx.text.ulTaskGenericNotifyTake), (8 bytes).
    Removing tasks.o(.text.xTaskGenericNotifyWait), (260 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskGenericNotifyWait), (8 bytes).
    Removing tasks.o(.text.xTaskGenericNotify), (256 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskGenericNotify), (8 bytes).
    Removing tasks.o(.text.xTaskGenericNotifyFromISR), (308 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskGenericNotifyFromISR), (8 bytes).
    Removing tasks.o(.text.vTaskGenericNotifyGiveFromISR), (220 bytes).
    Removing tasks.o(.ARM.exidx.text.vTaskGenericNotifyGiveFromISR), (8 bytes).
    Removing tasks.o(.text.xTaskGenericNotifyStateClear), (64 bytes).
    Removing tasks.o(.ARM.exidx.text.xTaskGenericNotifyStateClear), (8 bytes).
    Removing tasks.o(.text.ulTaskGenericNotifyValueClear), (40 bytes).
    Removing tasks.o(.ARM.exidx.text.ulTaskGenericNotifyValueClear), (8 bytes).
    Removing timers.o(.text), (0 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerCreateTimerTask), (8 bytes).
    Removing timers.o(.ARM.exidx.text.prvTimerTask), (8 bytes).
    Removing timers.o(.text.xTimerCreate), (164 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerCreate), (8 bytes).
    Removing timers.o(.text.xTimerGenericCommand), (96 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerGenericCommand), (8 bytes).
    Removing timers.o(.text.xTimerGetTimerDaemonTaskHandle), (20 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerGetTimerDaemonTaskHandle), (8 bytes).
    Removing timers.o(.text.xTimerGetPeriod), (12 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerGetPeriod), (8 bytes).
    Removing timers.o(.text.vTimerSetReloadMode), (40 bytes).
    Removing timers.o(.ARM.exidx.text.vTimerSetReloadMode), (8 bytes).
    Removing timers.o(.text.xTimerGetReloadMode), (32 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerGetReloadMode), (8 bytes).
    Removing timers.o(.text.uxTimerGetReloadMode), (32 bytes).
    Removing timers.o(.ARM.exidx.text.uxTimerGetReloadMode), (8 bytes).
    Removing timers.o(.text.xTimerGetExpiryTime), (12 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerGetExpiryTime), (8 bytes).
    Removing timers.o(.text.pcTimerGetName), (12 bytes).
    Removing timers.o(.ARM.exidx.text.pcTimerGetName), (8 bytes).
    Removing timers.o(.text.xTimerIsTimerActive), (32 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerIsTimerActive), (8 bytes).
    Removing timers.o(.text.pvTimerGetTimerID), (26 bytes).
    Removing timers.o(.ARM.exidx.text.pvTimerGetTimerID), (8 bytes).
    Removing timers.o(.text.vTimerSetTimerID), (26 bytes).
    Removing timers.o(.ARM.exidx.text.vTimerSetTimerID), (8 bytes).
    Removing timers.o(.text.xTimerPendFunctionCallFromISR), (40 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerPendFunctionCallFromISR), (8 bytes).
    Removing timers.o(.text.xTimerPendFunctionCall), (48 bytes).
    Removing timers.o(.ARM.exidx.text.xTimerPendFunctionCall), (8 bytes).
    Removing timers.o(.text.uxTimerGetTimerNumber), (4 bytes).
    Removing timers.o(.ARM.exidx.text.uxTimerGetTimerNumber), (8 bytes).
    Removing timers.o(.text.vTimerSetTimerNumber), (4 bytes).
    Removing timers.o(.ARM.exidx.text.vTimerSetTimerNumber), (8 bytes).
    Removing heap_4.o(.text), (0 bytes).
    Removing heap_4.o(.ARM.exidx.text.pvPortMalloc), (8 bytes).
    Removing heap_4.o(.ARM.exidx.text.vPortFree), (8 bytes).
    Removing heap_4.o(.text.xPortGetFreeHeapSize), (12 bytes).
    Removing heap_4.o(.ARM.exidx.text.xPortGetFreeHeapSize), (8 bytes).
    Removing heap_4.o(.text.xPortGetMinimumEverFreeHeapSize), (12 bytes).
    Removing heap_4.o(.ARM.exidx.text.xPortGetMinimumEverFreeHeapSize), (8 bytes).
    Removing heap_4.o(.text.vPortInitialiseBlocks), (2 bytes).
    Removing heap_4.o(.ARM.exidx.text.vPortInitialiseBlocks), (8 bytes).
    Removing heap_4.o(.text.pvPortCalloc), (50 bytes).
    Removing heap_4.o(.ARM.exidx.text.pvPortCalloc), (8 bytes).
    Removing heap_4.o(.text.vPortGetHeapStats), (120 bytes).
    Removing heap_4.o(.ARM.exidx.text.vPortGetHeapStats), (8 bytes).
    Removing port.o(.text), (0 bytes).
    Removing port.o(.ARM.exidx.text.pxPortInitialiseStack), (8 bytes).
    Removing port.o(.ARM.exidx.text.prvTaskExitError), (8 bytes).
    Removing port.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing port.o(.ARM.exidx.text.xPortStartScheduler), (8 bytes).
    Removing port.o(.ARM.exidx.text.vPortSetupTimerInterrupt), (8 bytes).
    Removing port.o(.ARM.exidx.text.vPortStartFirstTask), (8 bytes).
    Removing port.o(.text.vPortEndScheduler), (24 bytes).
    Removing port.o(.ARM.exidx.text.vPortEndScheduler), (8 bytes).
    Removing port.o(.ARM.exidx.text.vPortYield), (8 bytes).
    Removing port.o(.ARM.exidx.text.vPortEnterCritical), (8 bytes).
    Removing port.o(.ARM.exidx.text.vPortExitCritical), (8 bytes).
    Removing port.o(.ARM.exidx.text.ulSetInterruptMaskFromISR), (8 bytes).
    Removing port.o(.ARM.exidx.text.vClearInterruptMaskFromISR), (8 bytes).
    Removing port.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing port.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing startuptask.o(.text), (0 bytes).
    Removing startuptask.o(.ARM.exidx.text.StartupTask), (8 bytes).
    Removing startuptask.o(.ARM.exidx.text.RGB_task), (8 bytes).
    Removing startuptask.o(.ARM.exidx.text.OLED_task), (8 bytes).
    Removing startuptask.o(.ARM.exidx.text.main_task), (8 bytes).
    Removing startuptask.o(.ARM.exidx.text.line_task), (8 bytes).
    Removing startuptask.o(.ARM.exidx.text.Set0_task), (8 bytes).
    Removing startuptask.o(.ARM.exidx.text.circle_task), (8 bytes).
    Removing startuptask.o(.ARM.exidx.text.QEI_task), (8 bytes).
    Removing startuptask.o(.text.vApplicationStackOverflowHook), (2 bytes).
    Removing startuptask.o(.ARM.exidx.text.vApplicationStackOverflowHook), (8 bytes).
    Removing startuptask.o(.text.vApplicationMallocFailedHook), (2 bytes).
    Removing startuptask.o(.ARM.exidx.text.vApplicationMallocFailedHook), (8 bytes).
    Removing startuptask.o(.text.vApplicationIdleHook), (2 bytes).
    Removing startuptask.o(.ARM.exidx.text.vApplicationIdleHook), (8 bytes).
    Removing startuptask.o(.text.vApplicationTickHook), (2 bytes).
    Removing startuptask.o(.ARM.exidx.text.vApplicationTickHook), (8 bytes).
    Removing startuptask.o(.data.CyPwmOut), (4 bytes).
    Removing startuptask.o(.bss.test), (4 bytes).
    Removing startuptask.o(.bss.Set0_status), (4 bytes).
    Removing startuptask.o(.bss.Ccircle_flag), (4 bytes).
    Removing startuptask.o(.bss.LINE_MODE), (4 bytes).
    Removing startuptask.o(.bss.Cy_out), (4 bytes).
    Removing startuptask.o(.bss.QEI_Vaule), (4 bytes).
    Removing ws2812b.o(.text), (0 bytes).
    Removing ws2812b.o(.text.WS2812_RESET), (532 bytes).
    Removing ws2812b.o(.ARM.exidx.text.WS2812_RESET), (8 bytes).
    Removing ws2812b.o(.text.WS2812_Set_Color), (20 bytes).
    Removing ws2812b.o(.ARM.exidx.text.WS2812_Set_Color), (8 bytes).
    Removing ws2812b.o(.text.WS2812_Send_Array), (576 bytes).
    Removing ws2812b.o(.ARM.exidx.text.WS2812_Send_Array), (8 bytes).
    Removing ws2812b.o(.bss.LedsArray), (3 bytes).
    Removing uart.o(.text), (0 bytes).
    Removing servo.o(.text), (0 bytes).
    Removing servo.o(.text.DL_Timer_setCaptureCompareValue2), (16 bytes).
    Removing servo.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue2), (8 bytes).
    Removing servo.o(.text.Set_Servo), (80 bytes).
    Removing servo.o(.ARM.exidx.text.Set_Servo), (8 bytes).
    Removing servo.o(.text.Set_Servo_raw), (24 bytes).
    Removing servo.o(.ARM.exidx.text.Set_Servo_raw), (8 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing oled.o(.text.delay), (2 bytes).
    Removing oled.o(.ARM.exidx.text.delay), (8 bytes).
    Removing oled.o(.text.IIC_Start), (64 bytes).
    Removing oled.o(.ARM.exidx.text.IIC_Start), (8 bytes).
    Removing oled.o(.text.IIC_Stop), (44 bytes).
    Removing oled.o(.ARM.exidx.text.IIC_Stop), (8 bytes).
    Removing oled.o(.text.IIC_Ack), (32 bytes).
    Removing oled.o(.ARM.exidx.text.IIC_Ack), (8 bytes).
    Removing oled.o(.ARM.exidx.text.IIC_XIE), (8 bytes).
    Removing oled.o(.ARM.exidx.text.IIC_XIE_ML), (8 bytes).
    Removing oled.o(.ARM.exidx.text.IIC_XIE_DATA), (8 bytes).
    Removing oled.o(.text.OLED_Set_Pos), (30 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Set_Pos), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Clear), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowChar), (8 bytes).
    Removing oled.o(.text.OLED_ShowString), (74 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowString), (8 bytes).
    Removing oled.o(.text.OLED_ShowChinese), (272 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowChinese), (8 bytes).
    Removing oled.o(.text.OLED_DrawBMP), (328 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawBMP), (8 bytes).
    Removing oled.o(.text.OLED_IIC_Init), (2 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_IIC_Init), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Init), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Write), (8 bytes).
    Removing oled.o(.data.Hzk), (64 bytes).
    Removing oled.o(.data.Hzk2), (64 bytes).
    Removing motor.o(.text), (0 bytes).
    Removing motor.o(.text.Set_Motor), (140 bytes).
    Removing motor.o(.ARM.exidx.text.Set_Motor), (8 bytes).
    Removing motor.o(.ARM.exidx.text.Motor_Write), (8 bytes).
    Removing led_key.o(.text), (0 bytes).
    Removing led_key.o(.ARM.exidx.text.GPIO_WriteBit), (8 bytes).
    Removing enconder.o(.text), (0 bytes).
    Removing enconder.o(.ARM.exidx.text.Get_Encoder), (8 bytes).
    Removing enconder.o(.ARM.exidx.text.Encoder_init), (8 bytes).
    Removing enconder.o(.bss.Speed1_Temp), (2 bytes).
    Removing enconder.o(.bss.Speed2_Temp), (2 bytes).
    Removing delay.o(.text), (0 bytes).
    Removing delay.o(.ARM.exidx.text.delay_noOS), (8 bytes).
    Removing delay.o(.bss.delay_times), (4 bytes).
    Removing control.o(.text), (0 bytes).
    Removing control.o(.ARM.exidx.text.PID_Init), (8 bytes).
    Removing control.o(.text.Set_PID_1), (60 bytes).
    Removing control.o(.ARM.exidx.text.Set_PID_1), (8 bytes).
    Removing control.o(.text.Set_PID_2), (56 bytes).
    Removing control.o(.ARM.exidx.text.Set_PID_2), (8 bytes).
    Removing control.o(.ARM.exidx.text.Erect_pid), (8 bytes).
    Removing control.o(.text.Get_hw), (56 bytes).
    Removing control.o(.ARM.exidx.text.Get_hw), (8 bytes).
    Removing control.o(.ARM.exidx.text.Line_Control), (8 bytes).
    Removing control.o(.text.Motor_Open), (20 bytes).
    Removing control.o(.ARM.exidx.text.Motor_Open), (8 bytes).
    Removing control.o(.ARM.exidx.text.TASK_1), (8 bytes).
    Removing control.o(.ARM.exidx.text.TASK_3), (8 bytes).
    Removing control.o(.text.TASK_4), (12 bytes).
    Removing control.o(.ARM.exidx.text.TASK_4), (8 bytes).
    Removing control.o(.ARM.exidx.text.TI), (8 bytes).
    Removing control.o(.bss.b), (1 bytes).
    Removing maixcam.o(.text), (0 bytes).
    Removing maixcam.o(.text.UART2_Init), (20 bytes).
    Removing maixcam.o(.ARM.exidx.text.UART2_Init), (8 bytes).
    Removing maixcam.o(.text.uart2_send_char), (36 bytes).
    Removing maixcam.o(.ARM.exidx.text.uart2_send_char), (8 bytes).
    Removing maixcam.o(.text.uart2_send_string), (56 bytes).
    Removing maixcam.o(.ARM.exidx.text.uart2_send_string), (8 bytes).
    Removing maixcam.o(.text.fputc), (36 bytes).
    Removing maixcam.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing maixcam.o(.ARM.exidx.text.MaixCam_Init), (8 bytes).
    Removing maixcam.o(.ARM.exidx.text.MaixCam_Send_XY), (8 bytes).
    Removing maixcam.o(.ARM.exidx.text.UART3_IRQHandler), (8 bytes).
    Removing yunque.o(.text), (0 bytes).
    Removing yunque.o(.text.UART3_Init), (20 bytes).
    Removing yunque.o(.ARM.exidx.text.UART3_Init), (8 bytes).
    Removing yunque.o(.text.uart3_send_char), (36 bytes).
    Removing yunque.o(.ARM.exidx.text.uart3_send_char), (8 bytes).
    Removing yunque.o(.text.uart3_send_string), (56 bytes).
    Removing yunque.o(.ARM.exidx.text.uart3_send_string), (8 bytes).
    Removing yunque.o(.text.YunQue_Init), (20 bytes).
    Removing yunque.o(.ARM.exidx.text.YunQue_Init), (8 bytes).
    Removing yunque.o(.text.YunQue_CMD), (116 bytes).
    Removing yunque.o(.ARM.exidx.text.YunQue_CMD), (8 bytes).
    Removing emm_v5.o(.text), (0 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Speed_Control), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Vel_Control), (62 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Vel_Control), (8 bytes).
    Removing emm_v5.o(.ARM.exidx.text.POS_Control), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Pos_Control), (64 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Pos_Control), (8 bytes).
    Removing emm_v5.o(.text.usart_SendCmd), (8 bytes).
    Removing emm_v5.o(.ARM.exidx.text.usart_SendCmd), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Reset_CurPos_To_Zero), (44 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Reset_CurPos_To_Zero), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Reset_Clog_Pro), (44 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Reset_Clog_Pro), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Read_Sys_Params), (204 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Read_Sys_Params), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Modify_Ctrl_Mode), (54 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Modify_Ctrl_Mode), (8 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_En_Control), (8 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Stop_Now), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Synchronous_motion), (44 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Synchronous_motion), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Origin_Set_O), (50 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Set_O), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Origin_Modify_Params), (108 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Modify_Params), (8 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Trigger_Return), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Origin_Interrupt), (44 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Interrupt), (8 bytes).
    Removing emm_v5.o(.text.Wait_answer), (36 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Wait_answer), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Data_Dispose), (56 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Data_Dispose), (8 bytes).
    Removing emm_v5.o(.bss.rxFrameFlag), (1 bytes).
    Removing emm_v5.o(.bss..L_MergedGlobals), (8 bytes).
    Removing bsp_uart.o(.text), (0 bytes).
    Removing bsp_uart.o(.ARM.exidx.text.Uart_Send), (8 bytes).
    Removing fdiv.o(.text), (124 bytes).

1053 unused section(s) (total 39067 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/longlong.c                       0x00000000   Number         0  llmul.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmple.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmplt.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmpge.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmpgt.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\lib\ti\devices\msp\m0p\startup_system_files\keil\startup_mspm0g350x_uvision.s 0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    Emm_V5.c                                 0x00000000   Number         0  emm_v5.o ABSOLUTE
    Enconder.c                               0x00000000   Number         0  enconder.o ABSOLUTE
    LED_Key.c                                0x00000000   Number         0  led_key.o ABSOLUTE
    MaixCam.c                                0x00000000   Number         0  maixcam.o ABSOLUTE
    Motor.c                                  0x00000000   Number         0  motor.o ABSOLUTE
    StartupTask.c                            0x00000000   Number         0  startuptask.o ABSOLUTE
    UART.c                                   0x00000000   Number         0  uart.o ABSOLUTE
    YunQue.c                                 0x00000000   Number         0  yunque.o ABSOLUTE
    bsp_uart.c                               0x00000000   Number         0  bsp_uart.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    control.c                                0x00000000   Number         0  control.o ABSOLUTE
    croutine.c                               0x00000000   Number         0  croutine.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    delay.c                                  0x00000000   Number         0  delay.o ABSOLUTE
    dl_adc12.c                               0x00000000   Number         0  dl_adc12.o ABSOLUTE
    dl_aes.c                                 0x00000000   Number         0  dl_aes.o ABSOLUTE
    dl_aesadv.c                              0x00000000   Number         0  dl_aesadv.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_crc.c                                 0x00000000   Number         0  dl_crc.o ABSOLUTE
    dl_crcp.c                                0x00000000   Number         0  dl_crcp.o ABSOLUTE
    dl_dac12.c                               0x00000000   Number         0  dl_dac12.o ABSOLUTE
    dl_dma.c                                 0x00000000   Number         0  dl_dma.o ABSOLUTE
    dl_flashctl.c                            0x00000000   Number         0  dl_flashctl.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_interrupt.c                           0x00000000   Number         0  dl_interrupt.o ABSOLUTE
    dl_keystorectl.c                         0x00000000   Number         0  dl_keystorectl.o ABSOLUTE
    dl_lcd.c                                 0x00000000   Number         0  dl_lcd.o ABSOLUTE
    dl_lfss.c                                0x00000000   Number         0  dl_lfss.o ABSOLUTE
    dl_mathacl.c                             0x00000000   Number         0  dl_mathacl.o ABSOLUTE
    dl_mcan.c                                0x00000000   Number         0  dl_mcan.o ABSOLUTE
    dl_opa.c                                 0x00000000   Number         0  dl_opa.o ABSOLUTE
    dl_rtc_common.c                          0x00000000   Number         0  dl_rtc_common.o ABSOLUTE
    dl_spi.c                                 0x00000000   Number         0  dl_spi.o ABSOLUTE
    dl_sysctl_mspm0g1x0x_g3x0x.c             0x00000000   Number         0  dl_sysctl_mspm0g1x0x_g3x0x.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_trng.c                                0x00000000   Number         0  dl_trng.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    dl_vref.c                                0x00000000   Number         0  dl_vref.o ABSOLUTE
    event_groups.c                           0x00000000   Number         0  event_groups.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    heap_4.c                                 0x00000000   Number         0  heap_4.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    list.c                                   0x00000000   Number         0  list.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    oled.c                                   0x00000000   Number         0  oled.o ABSOLUTE
    port.c                                   0x00000000   Number         0  port.o ABSOLUTE
    queue.c                                  0x00000000   Number         0  queue.o ABSOLUTE
    servo.c                                  0x00000000   Number         0  servo.o ABSOLUTE
    tasks.c                                  0x00000000   Number         0  tasks.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    timers.c                                 0x00000000   Number         0  timers.o ABSOLUTE
    ws2812b.c                                0x00000000   Number         0  ws2812b.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    .ARM.Collect$$$$00000000                 0x000000c0   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x000000c0   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x000000c4   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x000000c8   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x000000c8   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x000000c8   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x000000d0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x000000d0   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x000000d0   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x000000d0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x000000d4   Section       20  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000000e8   Section        0  llmul.o(.text)
    .text                                    0x00000118   Section        0  memcpya.o(.text)
    .text                                    0x0000013c   Section        0  memseta.o(.text)
    .text                                    0x00000160   Section        0  fadd.o(.text)
    .text                                    0x00000212   Section        0  fmul.o(.text)
    .text                                    0x0000028c   Section        0  fcmple.o(.text)
    .text                                    0x000002a8   Section        0  fcmplt.o(.text)
    .text                                    0x000002c4   Section        0  fcmpge.o(.text)
    .text                                    0x000002e0   Section        0  fcmpgt.o(.text)
    .text                                    0x000002fc   Section        0  fflti.o(.text)
    .text                                    0x00000312   Section        0  ffltui.o(.text)
    .text                                    0x00000320   Section        0  ffixi.o(.text)
    .text                                    0x00000352   Section        0  ffixui.o(.text)
    .text                                    0x0000037a   Section        0  uidiv_div0.o(.text)
    .text                                    0x000003b8   Section        0  uldiv.o(.text)
    .text                                    0x00000418   Section        0  iusefp.o(.text)
    .text                                    0x00000418   Section        0  fepilogue.o(.text)
    .text                                    0x0000049c   Section        0  dadd.o(.text)
    .text                                    0x00000600   Section        0  dmul.o(.text)
    .text                                    0x000006d0   Section        0  ddiv.o(.text)
    .text                                    0x000007c0   Section        0  dfixul.o(.text)
    .text                                    0x00000800   Section       40  cdrcmple.o(.text)
    .text                                    0x00000828   Section       36  init.o(.text)
    .text                                    0x0000084c   Section        0  llshl.o(.text)
    .text                                    0x0000086c   Section        0  llushr.o(.text)
    .text                                    0x0000088e   Section        0  llsshr.o(.text)
    .text                                    0x000008b4   Section        0  depilogue.o(.text)
    [Anonymous Symbol]                       0x00000972   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    [Anonymous Symbol]                       0x0000097c   Section        0  dl_spi.o(.text.DL_SPI_init)
    __arm_cp.0_0                             0x000009b8   Number         4  dl_spi.o(.text.DL_SPI_init)
    __arm_cp.0_1                             0x000009bc   Number         4  dl_spi.o(.text.DL_SPI_init)
    [Anonymous Symbol]                       0x000009c0   Section        0  dl_spi.o(.text.DL_SPI_setClockConfig)
    [Anonymous Symbol]                       0x000009d4   Section        0  dl_timer.o(.text.DL_TimerA_initPWMMode)
    [Anonymous Symbol]                       0x00000ac8   Section        0  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_1                            0x00000b74   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_2                            0x00000b78   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_3                            0x00000b7c   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_4                            0x00000b80   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    __arm_cp.11_6                            0x00000b84   Number         4  dl_timer.o(.text.DL_Timer_initPWMMode)
    [Anonymous Symbol]                       0x00000b88   Section        0  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_0                             0x00000c64   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_1                             0x00000c68   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    __arm_cp.2_2                             0x00000c6c   Number         4  dl_timer.o(.text.DL_Timer_initTimerMode)
    [Anonymous Symbol]                       0x00000c70   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    __arm_cp.26_0                            0x00000c88   Number         4  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x00000c8c   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.13_0                            0x00000ca0   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x00000ca4   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.3_0                             0x00000cb0   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x00000cb4   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    __arm_cp.0_0                             0x00000ccc   Number         4  dl_timer.o(.text.DL_Timer_setClockConfig)
    [Anonymous Symbol]                       0x00000cd0   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x00000d10   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x00000d14   Number         4  dl_uart.o(.text.DL_UART_init)
    [Anonymous Symbol]                       0x00000d18   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    [Anonymous Symbol]                       0x00000d2c   Section        0  dl_uart.o(.text.DL_UART_transmitDataBlocking)
    __arm_cp.7_0                             0x00000d4c   Number         4  dl_uart.o(.text.DL_UART_transmitDataBlocking)
    [Anonymous Symbol]                       0x00000d50   Section        0  emm_v5.o(.text.Emm_V5_En_Control)
    [Anonymous Symbol]                       0x00000d86   Section        0  emm_v5.o(.text.Emm_V5_Origin_Trigger_Return)
    [Anonymous Symbol]                       0x00000db8   Section        0  emm_v5.o(.text.Emm_V5_Stop_Now)
    [Anonymous Symbol]                       0x00000dec   Section        0  enconder.o(.text.Encoder_init)
    __arm_cp.1_0                             0x00000df8   Number         4  enconder.o(.text.Encoder_init)
    [Anonymous Symbol]                       0x00000dfc   Section        0  control.o(.text.Erect_pid)
    [Anonymous Symbol]                       0x00000e54   Section        0  led_key.o(.text.GPIO_WriteBit)
    __arm_cp.0_0                             0x00000e6c   Number         4  led_key.o(.text.GPIO_WriteBit)
    [Anonymous Symbol]                       0x00000e70   Section        0  enconder.o(.text.Get_Encoder)
    __arm_cp.0_0                             0x00000e84   Number         4  enconder.o(.text.Get_Encoder)
    __arm_cp.0_1                             0x00000e88   Number         4  enconder.o(.text.Get_Encoder)
    [Anonymous Symbol]                       0x00000e8c   Section        0  oled.o(.text.IIC_XIE)
    [Anonymous Symbol]                       0x00000fcc   Section        0  oled.o(.text.IIC_XIE_DATA)
    [Anonymous Symbol]                       0x0000107c   Section        0  oled.o(.text.IIC_XIE_ML)
    __arm_cp.5_0                             0x0000112c   Number         4  oled.o(.text.IIC_XIE_ML)
    [Anonymous Symbol]                       0x00001130   Section        0  control.o(.text.Line_Control)
    __arm_cp.5_0                             0x00001200   Number         4  control.o(.text.Line_Control)
    __arm_cp.5_1                             0x00001204   Number         4  control.o(.text.Line_Control)
    __arm_cp.5_2                             0x00001208   Number         4  control.o(.text.Line_Control)
    __arm_cp.5_3                             0x0000120c   Number         4  control.o(.text.Line_Control)
    __arm_cp.5_4                             0x00001210   Number         4  control.o(.text.Line_Control)
    [Anonymous Symbol]                       0x00001214   Section        0  maixcam.o(.text.MaixCam_Init)
    __arm_cp.4_0                             0x00001220   Number         4  maixcam.o(.text.MaixCam_Init)
    __arm_cp.4_1                             0x00001224   Number         4  maixcam.o(.text.MaixCam_Init)
    [Anonymous Symbol]                       0x00001228   Section        0  maixcam.o(.text.MaixCam_Send_XY)
    __arm_cp.5_0                             0x000012d4   Number         4  maixcam.o(.text.MaixCam_Send_XY)
    [Anonymous Symbol]                       0x000012d8   Section        0  motor.o(.text.Motor_Write)
    __arm_cp.1_0                             0x00001364   Number         4  motor.o(.text.Motor_Write)
    __arm_cp.1_1                             0x00001368   Number         4  motor.o(.text.Motor_Write)
    __arm_cp.1_2                             0x0000136c   Number         4  motor.o(.text.Motor_Write)
    __arm_cp.1_3                             0x00001370   Number         4  motor.o(.text.Motor_Write)
    __arm_cp.1_4                             0x00001374   Number         4  motor.o(.text.Motor_Write)
    [Anonymous Symbol]                       0x00001378   Section        0  oled.o(.text.OLED_Clear)
    __arm_cp.8_0                             0x00001458   Number         4  oled.o(.text.OLED_Clear)
    [Anonymous Symbol]                       0x0000145c   Section        0  oled.o(.text.OLED_Init)
    __arm_cp.14_0                            0x00001560   Number         4  oled.o(.text.OLED_Init)
    [Anonymous Symbol]                       0x00001564   Section        0  oled.o(.text.OLED_ShowChar)
    __arm_cp.9_0                             0x00001658   Number         4  oled.o(.text.OLED_ShowChar)
    __arm_cp.9_1                             0x0000165c   Number         4  oled.o(.text.OLED_ShowChar)
    [Anonymous Symbol]                       0x00001660   Section        0  oled.o(.text.OLED_Write)
    __arm_cp.15_0                            0x000016ec   Number         4  oled.o(.text.OLED_Write)
    [Anonymous Symbol]                       0x000016f0   Section        0  startuptask.o(.text.OLED_task)
    [Anonymous Symbol]                       0x00001700   Section        0  control.o(.text.PID_Init)
    __arm_cp.0_0                             0x00001714   Number         4  control.o(.text.PID_Init)
    __arm_cp.0_1                             0x00001718   Number         4  control.o(.text.PID_Init)
    __arm_cp.0_2                             0x0000171c   Number         4  control.o(.text.PID_Init)
    __arm_cp.0_3                             0x00001720   Number         4  control.o(.text.PID_Init)
    __arm_cp.0_4                             0x00001724   Number         4  control.o(.text.PID_Init)
    [Anonymous Symbol]                       0x00001728   Section        0  emm_v5.o(.text.POS_Control)
    __arm_cp.2_0                             0x00001860   Number         4  emm_v5.o(.text.POS_Control)
    __arm_cp.2_1                             0x00001864   Number         4  emm_v5.o(.text.POS_Control)
    [Anonymous Symbol]                       0x00001870   Section        0  port.o(.text.PendSV_Handler)
    pxCurrentTCBConst                        0x000018b0   Number         0  port.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x000018b4   Section        0  startuptask.o(.text.QEI_task)
    __arm_cp.7_0                             0x000018e4   Number         4  startuptask.o(.text.QEI_task)
    [Anonymous Symbol]                       0x000018e8   Section        0  startuptask.o(.text.RGB_task)
    [Anonymous Symbol]                       0x000018f8   Section        0  port.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x000018fc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x000019a4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x000019a8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x000019ac   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_3                             0x000019b0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_4                             0x000019b4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_5                             0x000019b8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_6                             0x000019bc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_7                             0x000019c0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_8                             0x000019c4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_9                             0x000019c8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x000019cc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_0                             0x00001a44   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_1                             0x00001a48   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_2                             0x00001a4c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_3                             0x00001a50   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_5                             0x00001a54   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    __arm_cp.4_6                             0x00001a58   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    [Anonymous Symbol]                       0x00001a5c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    __arm_cp.6_0                             0x00001a88   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    __arm_cp.6_1                             0x00001a8c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    __arm_cp.6_2                             0x00001a90   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    __arm_cp.6_3                             0x00001a94   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    __arm_cp.6_4                             0x00001a98   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    __arm_cp.6_5                             0x00001a9c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    __arm_cp.6_6                             0x00001aa0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    __arm_cp.6_7                             0x00001aa4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    [Anonymous Symbol]                       0x00001aa8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init)
    __arm_cp.11_0                            0x00001ae0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init)
    __arm_cp.11_1                            0x00001ae4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init)
    __arm_cp.11_2                            0x00001ae8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init)
    __arm_cp.11_3                            0x00001aec   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init)
    __arm_cp.11_4                            0x00001af0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init)
    [Anonymous Symbol]                       0x00001af4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x00001b28   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_1                             0x00001b2c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00001b30   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x00001b34   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init)
    __arm_cp.5_0                             0x00001bf0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init)
    __arm_cp.5_1                             0x00001bf4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init)
    __arm_cp.5_2                             0x00001bf8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init)
    __arm_cp.5_3                             0x00001bfc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init)
    __arm_cp.5_4                             0x00001c00   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init)
    __arm_cp.5_5                             0x00001c04   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init)
    [Anonymous Symbol]                       0x00001c08   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.7_0                             0x00001c38   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.7_1                             0x00001c3c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.7_2                             0x00001c40   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.7_3                             0x00001c44   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.7_4                             0x00001c48   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    __arm_cp.7_5                             0x00001c4c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    [Anonymous Symbol]                       0x00001c50   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_1                             0x00001cb8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_2                             0x00001cbc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_3                             0x00001cc0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_5                             0x00001cc4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_6                             0x00001cc8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x00001ccc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.9_1                             0x00001d24   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.9_2                             0x00001d28   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.9_3                             0x00001d2c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.9_5                             0x00001d30   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    [Anonymous Symbol]                       0x00001d34   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    __arm_cp.10_0                            0x00001d98   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    __arm_cp.10_1                            0x00001d9c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    __arm_cp.10_2                            0x00001da0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    __arm_cp.10_3                            0x00001da4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    __arm_cp.10_4                            0x00001da8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    __arm_cp.10_5                            0x00001dac   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    __arm_cp.10_6                            0x00001db0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    [Anonymous Symbol]                       0x00001db4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x00001e04   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_1                             0x00001e08   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_2                             0x00001e0c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_3                             0x00001e10   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00001e14   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x00001e6c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x00001e70   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x00001e74   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x00001e78   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_4                             0x00001e7c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_5                             0x00001e80   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_6                             0x00001e84   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_7                             0x00001e88   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_8                             0x00001e8c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_9                             0x00001e90   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_10                            0x00001e94   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_11                            0x00001e98   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x00001e9c   Section        0  startuptask.o(.text.Set0_task)
    __arm_cp.5_0                             0x00001f98   Number         4  startuptask.o(.text.Set0_task)
    __arm_cp.5_1                             0x00001f9c   Number         4  startuptask.o(.text.Set0_task)
    [Anonymous Symbol]                       0x00001fa0   Section        0  emm_v5.o(.text.Speed_Control)
    __arm_cp.0_0                             0x000020d4   Number         4  emm_v5.o(.text.Speed_Control)
    __arm_cp.0_1                             0x000020d8   Number         4  emm_v5.o(.text.Speed_Control)
    [Anonymous Symbol]                       0x000020dc   Section        0  startuptask.o(.text.StartupTask)
    __arm_cp.0_0                             0x00002180   Number         4  startuptask.o(.text.StartupTask)
    __arm_cp.0_1                             0x00002184   Number         4  startuptask.o(.text.StartupTask)
    __arm_cp.0_3                             0x0000218c   Number         4  startuptask.o(.text.StartupTask)
    __arm_cp.0_5                             0x00002198   Number         4  startuptask.o(.text.StartupTask)
    __arm_cp.0_7                             0x000021a4   Number         4  startuptask.o(.text.StartupTask)
    __arm_cp.0_9                             0x000021b0   Number         4  startuptask.o(.text.StartupTask)
    __arm_cp.0_11                            0x000021bc   Number         4  startuptask.o(.text.StartupTask)
    __arm_cp.0_13                            0x000021c8   Number         4  startuptask.o(.text.StartupTask)
    [Anonymous Symbol]                       0x000021d0   Section        0  port.o(.text.SysTick_Handler)
    __arm_cp.13_0                            0x000021f0   Number         4  port.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x000021f4   Section        0  control.o(.text.TASK_1)
    __arm_cp.7_0                             0x000022a8   Number         4  control.o(.text.TASK_1)
    __arm_cp.7_1                             0x000022ac   Number         4  control.o(.text.TASK_1)
    __arm_cp.7_2                             0x000022b0   Number         4  control.o(.text.TASK_1)
    __arm_cp.7_4                             0x000022b8   Number         4  control.o(.text.TASK_1)
    [Anonymous Symbol]                       0x000022bc   Section        0  control.o(.text.TASK_3)
    __arm_cp.8_0                             0x00002320   Number         4  control.o(.text.TASK_3)
    __arm_cp.8_3                             0x0000232c   Number         4  control.o(.text.TASK_3)
    [Anonymous Symbol]                       0x00002330   Section        0  control.o(.text.TI)
    __arm_cp.10_7                            0x00002680   Number         4  control.o(.text.TI)
    __arm_cp.10_8                            0x00002684   Number         4  control.o(.text.TI)
    __arm_cp.10_9                            0x00002688   Number         4  control.o(.text.TI)
    __arm_cp.10_10                           0x0000268c   Number         4  control.o(.text.TI)
    __arm_cp.10_11                           0x00002690   Number         4  control.o(.text.TI)
    [Anonymous Symbol]                       0x00002694   Section        0  maixcam.o(.text.UART3_IRQHandler)
    __arm_cp.6_0                             0x00002710   Number         4  maixcam.o(.text.UART3_IRQHandler)
    __arm_cp.6_1                             0x00002714   Number         4  maixcam.o(.text.UART3_IRQHandler)
    __arm_cp.6_2                             0x00002718   Number         4  maixcam.o(.text.UART3_IRQHandler)
    __arm_cp.6_3                             0x0000271c   Number         4  maixcam.o(.text.UART3_IRQHandler)
    [Anonymous Symbol]                       0x00002720   Section        0  bsp_uart.o(.text.Uart_Send)
    [Anonymous Symbol]                       0x0000273c   Section        0  startuptask.o(.text.circle_task)
    [Anonymous Symbol]                       0x00002744   Section        0  delay.o(.text.delay_noOS)
    __arm_cp.0_0                             0x000027bc   Number         4  delay.o(.text.delay_noOS)
    [Anonymous Symbol]                       0x000027c0   Section        0  startuptask.o(.text.line_task)
    __arm_cp.4_0                             0x00002828   Number         4  startuptask.o(.text.line_task)
    __arm_cp.4_1                             0x0000282c   Number         4  startuptask.o(.text.line_task)
    __arm_cp.4_2                             0x00002830   Number         4  startuptask.o(.text.line_task)
    __arm_cp.4_3                             0x00002834   Number         4  startuptask.o(.text.line_task)
    [Anonymous Symbol]                       0x00002838   Section        0  main.o(.text.main)
    [Anonymous Symbol]                       0x00002874   Section        0  startuptask.o(.text.main_task)
    __arm_cp.3_0                             0x00002bdc   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_1                             0x00002be0   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_2                             0x00002be4   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_3                             0x00002be8   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_4                             0x00002bec   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_5                             0x00002bf0   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_6                             0x00002bf4   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_7                             0x00002bf8   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_8                             0x00002bfc   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_9                             0x00002c00   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_10                            0x00002c04   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_11                            0x00002c08   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_12                            0x00002c0c   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_13                            0x00002c10   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_14                            0x00002c14   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_15                            0x00002c18   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_16                            0x00002c1c   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_17                            0x00002c20   Number         4  startuptask.o(.text.main_task)
    __arm_cp.3_18                            0x00002c24   Number         4  startuptask.o(.text.main_task)
    prvIdleTask                              0x00002c29   Thumb Code    68  tasks.o(.text.prvIdleTask)
    [Anonymous Symbol]                       0x00002c28   Section        0  tasks.o(.text.prvIdleTask)
    __arm_cp.15_0                            0x00002c6c   Number         4  tasks.o(.text.prvIdleTask)
    __arm_cp.15_1                            0x00002c70   Number         4  tasks.o(.text.prvIdleTask)
    prvTaskExitError                         0x00002c75   Thumb Code    48  port.o(.text.prvTaskExitError)
    [Anonymous Symbol]                       0x00002c74   Section        0  port.o(.text.prvTaskExitError)
    __arm_cp.1_0                             0x00002ca4   Number         4  port.o(.text.prvTaskExitError)
    prvTimerTask                             0x00002ca9   Thumb Code   764  timers.o(.text.prvTimerTask)
    [Anonymous Symbol]                       0x00002ca8   Section        0  timers.o(.text.prvTimerTask)
    __arm_cp.1_0                             0x00002fa4   Number         4  timers.o(.text.prvTimerTask)
    prvUnlockQueue                           0x00002fa9   Thumb Code   126  queue.o(.text.prvUnlockQueue)
    [Anonymous Symbol]                       0x00002fa8   Section        0  queue.o(.text.prvUnlockQueue)
    [Anonymous Symbol]                       0x00003028   Section        0  heap_4.o(.text.pvPortMalloc)
    __arm_cp.0_1                             0x0000319c   Number         4  heap_4.o(.text.pvPortMalloc)
    [Anonymous Symbol]                       0x000031a0   Section        0  port.o(.text.pxPortInitialiseStack)
    __arm_cp.0_0                             0x000031c0   Number         4  port.o(.text.pxPortInitialiseStack)
    [Anonymous Symbol]                       0x000031c4   Section        0  port.o(.text.ulSetInterruptMaskFromISR)
    [Anonymous Symbol]                       0x000031cc   Section        0  list.o(.text.uxListRemove)
    [Anonymous Symbol]                       0x000031ee   Section        0  port.o(.text.vClearInterruptMaskFromISR)
    [Anonymous Symbol]                       0x000031f4   Section        0  list.o(.text.vListInitialise)
    [Anonymous Symbol]                       0x00003208   Section        0  list.o(.text.vListInitialiseItem)
    [Anonymous Symbol]                       0x0000320e   Section        0  list.o(.text.vListInsert)
    [Anonymous Symbol]                       0x0000324c   Section        0  port.o(.text.vPortEnterCritical)
    [Anonymous Symbol]                       0x00003260   Section        0  port.o(.text.vPortExitCritical)
    __arm_cp.9_0                             0x00003278   Number         4  port.o(.text.vPortExitCritical)
    [Anonymous Symbol]                       0x0000327c   Section        0  heap_4.o(.text.vPortFree)
    __arm_cp.1_0                             0x00003310   Number         4  heap_4.o(.text.vPortFree)
    [Anonymous Symbol]                       0x00003314   Section        0  port.o(.text.vPortSetupTimerInterrupt)
    __arm_cp.4_0                             0x00003328   Number         4  port.o(.text.vPortSetupTimerInterrupt)
    __arm_cp.4_1                             0x0000332c   Number         4  port.o(.text.vPortSetupTimerInterrupt)
    vPortStartFirstTask                      0x00003331   Thumb Code    52  port.o(.text.vPortStartFirstTask)
    [Anonymous Symbol]                       0x00003330   Section        0  port.o(.text.vPortStartFirstTask)
    pxCurrentTCBConst2                       0x00003360   Number         0  port.o(.text.vPortStartFirstTask)
    [Anonymous Symbol]                       0x00003364   Section        0  port.o(.text.vPortYield)
    __arm_cp.7_0                             0x00003378   Number         4  port.o(.text.vPortYield)
    [Anonymous Symbol]                       0x0000337c   Section        0  queue.o(.text.vQueueAddToRegistry)
    __arm_cp.27_0                            0x00003470   Number         4  queue.o(.text.vQueueAddToRegistry)
    [Anonymous Symbol]                       0x00003474   Section        0  queue.o(.text.vQueueWaitForMessageRestricted)
    [Anonymous Symbol]                       0x000034d0   Section        0  tasks.o(.text.vTaskDelay)
    [Anonymous Symbol]                       0x00003544   Section        0  tasks.o(.text.vTaskInternalSetTimeOutState)
    [Anonymous Symbol]                       0x00003550   Section        0  tasks.o(.text.vTaskMissedYield)
    [Anonymous Symbol]                       0x00003558   Section        0  tasks.o(.text.vTaskPlaceOnEventList)
    [Anonymous Symbol]                       0x000035d8   Section        0  tasks.o(.text.vTaskPlaceOnEventListRestricted)
    [Anonymous Symbol]                       0x00003678   Section        0  tasks.o(.text.vTaskStartScheduler)
    __arm_cp.14_0                            0x000036c0   Number         4  tasks.o(.text.vTaskStartScheduler)
    __arm_cp.14_1                            0x000036c4   Number         4  tasks.o(.text.vTaskStartScheduler)
    __arm_cp.14_3                            0x000036d0   Number         4  tasks.o(.text.vTaskStartScheduler)
    [Anonymous Symbol]                       0x000036d4   Section        0  tasks.o(.text.vTaskSuspendAll)
    __tagsym$$used.0                         0x000036e0   Number         0  tasks.o(.text.vTaskSwitchContext)
    [Anonymous Symbol]                       0x000036e0   Section        0  tasks.o(.text.vTaskSwitchContext)
    __arm_cp.11_0                            0x00003768   Number         4  tasks.o(.text.vTaskSwitchContext)
    __arm_cp.11_1                            0x0000376c   Number         4  tasks.o(.text.vTaskSwitchContext)
    __arm_cp.11_2                            0x00003770   Number         4  tasks.o(.text.vTaskSwitchContext)
    [Anonymous Symbol]                       0x00003774   Section        0  port.o(.text.xPortStartScheduler)
    __arm_cp.3_0                             0x000037c8   Number         4  port.o(.text.xPortStartScheduler)
    __arm_cp.3_1                             0x000037cc   Number         4  port.o(.text.xPortStartScheduler)
    [Anonymous Symbol]                       0x000037d0   Section        0  queue.o(.text.xQueueGenericCreate)
    [Anonymous Symbol]                       0x00003866   Section        0  queue.o(.text.xQueueReceive)
    [Anonymous Symbol]                       0x000039a8   Section        0  tasks.o(.text.xTaskCheckForTimeOut)
    __arm_cp.31_0                            0x00003a1c   Number         4  tasks.o(.text.xTaskCheckForTimeOut)
    [Anonymous Symbol]                       0x00003a20   Section        0  tasks.o(.text.xTaskCreate)
    __arm_cp.0_1                             0x00003e4c   Number         4  tasks.o(.text.xTaskCreate)
    __arm_cp.0_2                             0x00003e50   Number         4  tasks.o(.text.xTaskCreate)
    __arm_cp.0_3                             0x00003e54   Number         4  tasks.o(.text.xTaskCreate)
    __arm_cp.0_4                             0x00003e58   Number         4  tasks.o(.text.xTaskCreate)
    __arm_cp.0_5                             0x00003e5c   Number         4  tasks.o(.text.xTaskCreate)
    [Anonymous Symbol]                       0x00003e60   Section        0  tasks.o(.text.xTaskGetSchedulerState)
    [Anonymous Symbol]                       0x00003e78   Section        0  tasks.o(.text.xTaskGetTickCount)
    __arm_cp.18_0                            0x00003e80   Number         4  tasks.o(.text.xTaskGetTickCount)
    [Anonymous Symbol]                       0x00003e84   Section        0  tasks.o(.text.xTaskIncrementTick)
    [Anonymous Symbol]                       0x00003fb4   Section        0  tasks.o(.text.xTaskRemoveFromEventList)
    [Anonymous Symbol]                       0x00004060   Section        0  tasks.o(.text.xTaskResumeAll)
    __arm_cp.4_0                             0x00004168   Number         4  tasks.o(.text.xTaskResumeAll)
    __arm_cp.4_1                             0x0000416c   Number         4  tasks.o(.text.xTaskResumeAll)
    __arm_cp.4_2                             0x00004170   Number         4  tasks.o(.text.xTaskResumeAll)
    [Anonymous Symbol]                       0x00004174   Section        0  timers.o(.text.xTimerCreateTimerTask)
    __arm_cp.0_0                             0x000041e0   Number         4  timers.o(.text.xTimerCreateTimerTask)
    __arm_cp.0_1                             0x000041e4   Number         4  timers.o(.text.xTimerCreateTimerTask)
    __arm_cp.0_2                             0x000041e8   Number         4  timers.o(.text.xTimerCreateTimerTask)
    i.__0vsprintf                            0x000041f4   Section        0  printfa.o(i.__0vsprintf)
    i.__ARM_clz                              0x00004218   Section        0  depilogue.o(i.__ARM_clz)
    i.__scatterload_copy                     0x00004248   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x00004258   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x00004260   Section       14  handlers.o(i.__scatterload_zeroinit)
    _fp_digits                               0x00004271   Thumb Code   344  printfa.o(i._fp_digits)
    i._fp_digits                             0x00004270   Section        0  printfa.o(i._fp_digits)
    _printf_core                             0x000043e5   Thumb Code  1754  printfa.o(i._printf_core)
    i._printf_core                           0x000043e4   Section        0  printfa.o(i._printf_core)
    _printf_post_padding                     0x00004ad1   Thumb Code    32  printfa.o(i._printf_post_padding)
    i._printf_post_padding                   0x00004ad0   Section        0  printfa.o(i._printf_post_padding)
    _printf_pre_padding                      0x00004af1   Thumb Code    44  printfa.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x00004af0   Section        0  printfa.o(i._printf_pre_padding)
    _sputc                                   0x00004b1d   Thumb Code    10  printfa.o(i._sputc)
    i._sputc                                 0x00004b1c   Section        0  printfa.o(i._sputc)
    gMOTOR_PWMClockConfig                    0x0000533e   Data           3  ti_msp_dl_config.o(.rodata.gMOTOR_PWMClockConfig)
    [Anonymous Symbol]                       0x0000533e   Section        0  ti_msp_dl_config.o(.rodata.gMOTOR_PWMClockConfig)
    gMOTOR_PWMConfig                         0x00005344   Data           8  ti_msp_dl_config.o(.rodata.gMOTOR_PWMConfig)
    [Anonymous Symbol]                       0x00005344   Section        0  ti_msp_dl_config.o(.rodata.gMOTOR_PWMConfig)
    gQEI_0ClockConfig                        0x0000534c   Data           3  ti_msp_dl_config.o(.rodata.gQEI_0ClockConfig)
    [Anonymous Symbol]                       0x0000534c   Section        0  ti_msp_dl_config.o(.rodata.gQEI_0ClockConfig)
    gSPI_WS2812_clockConfig                  0x0000534f   Data           2  ti_msp_dl_config.o(.rodata.gSPI_WS2812_clockConfig)
    [Anonymous Symbol]                       0x0000534f   Section        0  ti_msp_dl_config.o(.rodata.gSPI_WS2812_clockConfig)
    gSPI_WS2812_config                       0x00005352   Data          10  ti_msp_dl_config.o(.rodata.gSPI_WS2812_config)
    [Anonymous Symbol]                       0x00005352   Section        0  ti_msp_dl_config.o(.rodata.gSPI_WS2812_config)
    gServoClockConfig                        0x0000535c   Data           3  ti_msp_dl_config.o(.rodata.gServoClockConfig)
    [Anonymous Symbol]                       0x0000535c   Section        0  ti_msp_dl_config.o(.rodata.gServoClockConfig)
    gServoConfig                             0x00005360   Data           8  ti_msp_dl_config.o(.rodata.gServoConfig)
    [Anonymous Symbol]                       0x00005360   Section        0  ti_msp_dl_config.o(.rodata.gServoConfig)
    gTIMER_0ClockConfig                      0x00005368   Data           3  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    [Anonymous Symbol]                       0x00005368   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0ClockConfig)
    gTIMER_0TimerConfig                      0x0000536c   Data          20  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    [Anonymous Symbol]                       0x0000536c   Section        0  ti_msp_dl_config.o(.rodata.gTIMER_0TimerConfig)
    gUART_0ClockConfig                       0x00005380   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x00005380   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x00005382   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x00005382   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    gUART_1ClockConfig                       0x0000538c   Data           2  ti_msp_dl_config.o(.rodata.gUART_1ClockConfig)
    [Anonymous Symbol]                       0x0000538c   Section        0  ti_msp_dl_config.o(.rodata.gUART_1ClockConfig)
    gUART_1Config                            0x0000538e   Data          10  ti_msp_dl_config.o(.rodata.gUART_1Config)
    [Anonymous Symbol]                       0x0000538e   Section        0  ti_msp_dl_config.o(.rodata.gUART_1Config)
    gUART_3ClockConfig                       0x00005398   Data           2  ti_msp_dl_config.o(.rodata.gUART_3ClockConfig)
    [Anonymous Symbol]                       0x00005398   Section        0  ti_msp_dl_config.o(.rodata.gUART_3ClockConfig)
    gUART_3Config                            0x0000539a   Data          10  ti_msp_dl_config.o(.rodata.gUART_3Config)
    [Anonymous Symbol]                       0x0000539a   Section        0  ti_msp_dl_config.o(.rodata.gUART_3Config)
    [Anonymous Symbol]                       0x000053a4   Section        0  timers.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x000053a9   Section        0  control.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x20200000   Section        0  startuptask.o(.data..L_MergedGlobals)
    uxCriticalNesting                        0x20200018   Data           4  port.o(.data.uxCriticalNesting)
    [Anonymous Symbol]                       0x20200018   Section        0  port.o(.data.uxCriticalNesting)
    [Anonymous Symbol]                       0x20200020   Section        0  ti_msp_dl_config.o(.bss..L_MergedGlobals)
    uxTaskNumber                             0x20200078   Data           4  tasks.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20200078   Section        0  tasks.o(.bss..L_MergedGlobals)
    uxDeletedTasksWaitingCleanUp             0x2020007c   Data           4  tasks.o(.bss..L_MergedGlobals)
    uxCurrentNumberOfTasks                   0x20200080   Data           4  tasks.o(.bss..L_MergedGlobals)
    xSchedulerRunning                        0x20200084   Data           4  tasks.o(.bss..L_MergedGlobals)
    uxSchedulerSuspended                     0x20200088   Data           4  tasks.o(.bss..L_MergedGlobals)
    xTickCount                               0x2020008c   Data           4  tasks.o(.bss..L_MergedGlobals)
    pxDelayedTaskList                        0x20200090   Data           4  tasks.o(.bss..L_MergedGlobals)
    pxOverflowDelayedTaskList                0x20200094   Data           4  tasks.o(.bss..L_MergedGlobals)
    uxTopReadyPriority                       0x20200098   Data           4  tasks.o(.bss..L_MergedGlobals)
    xYieldPending                            0x2020009c   Data           4  tasks.o(.bss..L_MergedGlobals)
    xIdleTaskHandle                          0x202000a0   Data           4  tasks.o(.bss..L_MergedGlobals)
    xNextTaskUnblockTime                     0x202000a4   Data           4  tasks.o(.bss..L_MergedGlobals)
    xPendedTicks                             0x202000a8   Data           4  tasks.o(.bss..L_MergedGlobals)
    xNumOfOverflows                          0x202000ac   Data           4  tasks.o(.bss..L_MergedGlobals)
    xTasksWaitingTermination                 0x202000b0   Data          20  tasks.o(.bss..L_MergedGlobals)
    xSuspendedTaskList                       0x202000c4   Data          20  tasks.o(.bss..L_MergedGlobals)
    xPendingReadyList                        0x202000d8   Data          20  tasks.o(.bss..L_MergedGlobals)
    xTimerQueue                              0x202000ec   Data           4  timers.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x202000ec   Section        0  timers.o(.bss..L_MergedGlobals)
    xTimerTaskHandle                         0x202000f0   Data           4  timers.o(.bss..L_MergedGlobals)
    pxCurrentTimerList                       0x202000f4   Data           4  timers.o(.bss..L_MergedGlobals)
    pxOverflowTimerList                      0x202000f8   Data           4  timers.o(.bss..L_MergedGlobals)
    prvSampleTimeNow.xLastTime               0x202000fc   Data           4  timers.o(.bss..L_MergedGlobals)
    xActiveTimerList1                        0x20200100   Data          20  timers.o(.bss..L_MergedGlobals)
    xActiveTimerList2                        0x20200114   Data          20  timers.o(.bss..L_MergedGlobals)
    pxEnd                                    0x20200128   Data           4  heap_4.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20200128   Section        0  heap_4.o(.bss..L_MergedGlobals)
    xFreeBytesRemaining                      0x2020012c   Data           4  heap_4.o(.bss..L_MergedGlobals)
    xMinimumEverFreeBytesRemaining           0x20200130   Data           4  heap_4.o(.bss..L_MergedGlobals)
    xNumberOfSuccessfulAllocations           0x20200134   Data           4  heap_4.o(.bss..L_MergedGlobals)
    xNumberOfSuccessfulFrees                 0x20200138   Data           4  heap_4.o(.bss..L_MergedGlobals)
    xStart                                   0x2020013c   Data           8  heap_4.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20200144   Section        0  control.o(.bss..L_MergedGlobals)
    UART3_IRQHandler.i                       0x2020019c   Data           1  maixcam.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x2020019c   Section        0  maixcam.o(.bss..L_MergedGlobals)
    UART3_IRQHandler.dataFlag                0x2020019d   Data           1  maixcam.o(.bss..L_MergedGlobals)
    UART3_IRQHandler.MaixCam_data_buff       0x202001ac   Data          36  maixcam.o(.bss..L_MergedGlobals)
    xDelayedTaskList1                        0x202001d0   Data          20  tasks.o(.bss..L_MergedGlobals.1)
    [Anonymous Symbol]                       0x202001d0   Section        0  tasks.o(.bss..L_MergedGlobals.1)
    xDelayedTaskList2                        0x202001e4   Data          20  tasks.o(.bss..L_MergedGlobals.1)
    [Anonymous Symbol]                       0x202001f8   Section        0  startuptask.o(.bss..L_MergedGlobals.7)
    RGBTask_Handler                          0x202001fc   Data           4  startuptask.o(.bss..L_MergedGlobals.7)
    OLEDTask_Handler                         0x20200200   Data           4  startuptask.o(.bss..L_MergedGlobals.7)
    mainTask_Handler                         0x20200204   Data           4  startuptask.o(.bss..L_MergedGlobals.7)
    lineTask_Handler                         0x20200208   Data           4  startuptask.o(.bss..L_MergedGlobals.7)
    Set0Task_Handler                         0x2020020c   Data           4  startuptask.o(.bss..L_MergedGlobals.7)
    Circle_Task_Handler                      0x20200210   Data           4  startuptask.o(.bss..L_MergedGlobals.7)
    QEI_Task_Handler                         0x20200214   Data           4  startuptask.o(.bss..L_MergedGlobals.7)
    __tagsym$$used.1                         0x202003f8   Number         0  tasks.o(.bss.pxCurrentTCB)
    pxReadyTasksLists                        0x202003fc   Data        1120  tasks.o(.bss.pxReadyTasksLists)
    [Anonymous Symbol]                       0x202003fc   Section        0  tasks.o(.bss.pxReadyTasksLists)
    ucHeap                                   0x2020085c   Data       20480  heap_4.o(.bss.ucHeap)
    [Anonymous Symbol]                       0x2020085c   Section        0  heap_4.o(.bss.ucHeap)
    STACK                                    0x202058a0   Section     2048  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x000000c1   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x000000c5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x000000c9   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x000000c9   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x000000c9   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x000000c9   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x000000d1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x000000d1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x000000d5   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x000000d9   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x000000db   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x000000e3   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    GROUP1_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART1_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    UART2_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __aeabi_lmul                             0x000000e9   Thumb Code    48  llmul.o(.text)
    _ll_mul                                  0x000000e9   Thumb Code     0  llmul.o(.text)
    __aeabi_memcpy                           0x00000119   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x00000119   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x00000119   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0000013d   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0000013d   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0000013d   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0000014b   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0000014b   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0000014b   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0000014f   Thumb Code    18  memseta.o(.text)
    __aeabi_fadd                             0x00000161   Thumb Code   162  fadd.o(.text)
    __aeabi_fsub                             0x00000203   Thumb Code     8  fadd.o(.text)
    __aeabi_frsub                            0x0000020b   Thumb Code     8  fadd.o(.text)
    __aeabi_fmul                             0x00000213   Thumb Code   122  fmul.o(.text)
    __aeabi_fcmple                           0x0000028d   Thumb Code    28  fcmple.o(.text)
    __aeabi_fcmplt                           0x000002a9   Thumb Code    28  fcmplt.o(.text)
    __aeabi_fcmpge                           0x000002c5   Thumb Code    28  fcmpge.o(.text)
    __aeabi_fcmpgt                           0x000002e1   Thumb Code    28  fcmpgt.o(.text)
    __aeabi_i2f                              0x000002fd   Thumb Code    22  fflti.o(.text)
    __aeabi_ui2f                             0x00000313   Thumb Code    14  ffltui.o(.text)
    __aeabi_f2iz                             0x00000321   Thumb Code    50  ffixi.o(.text)
    __aeabi_f2uiz                            0x00000353   Thumb Code    40  ffixui.o(.text)
    __aeabi_uidiv                            0x0000037b   Thumb Code     0  uidiv_div0.o(.text)
    __aeabi_uidivmod                         0x0000037b   Thumb Code    62  uidiv_div0.o(.text)
    __aeabi_uldivmod                         0x000003b9   Thumb Code    96  uldiv.o(.text)
    __I$use$fp                               0x00000419   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x00000419   Thumb Code    16  fepilogue.o(.text)
    _float_epilogue                          0x00000429   Thumb Code   114  fepilogue.o(.text)
    __aeabi_dadd                             0x0000049d   Thumb Code   328  dadd.o(.text)
    __aeabi_dsub                             0x000005e5   Thumb Code    12  dadd.o(.text)
    __aeabi_drsub                            0x000005f1   Thumb Code    12  dadd.o(.text)
    __aeabi_dmul                             0x00000601   Thumb Code   202  dmul.o(.text)
    __aeabi_ddiv                             0x000006d1   Thumb Code   234  ddiv.o(.text)
    __aeabi_d2ulz                            0x000007c1   Thumb Code    54  dfixul.o(.text)
    __aeabi_cdrcmple                         0x00000801   Thumb Code    38  cdrcmple.o(.text)
    __scatterload                            0x00000829   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x00000829   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x0000084d   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x0000084d   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x0000086d   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x0000086d   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x0000088f   Thumb Code    38  llsshr.o(.text)
    _ll_sshift_r                             0x0000088f   Thumb Code     0  llsshr.o(.text)
    _double_round                            0x000008b5   Thumb Code    26  depilogue.o(.text)
    _double_epilogue                         0x000008cf   Thumb Code   164  depilogue.o(.text)
    DL_Common_delayCycles                    0x00000973   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_SPI_init                              0x0000097d   Thumb Code    60  dl_spi.o(.text.DL_SPI_init)
    DL_SPI_setClockConfig                    0x000009c1   Thumb Code    18  dl_spi.o(.text.DL_SPI_setClockConfig)
    DL_TimerA_initPWMMode                    0x000009d5   Thumb Code   244  dl_timer.o(.text.DL_TimerA_initPWMMode)
    DL_Timer_initPWMMode                     0x00000ac9   Thumb Code   172  dl_timer.o(.text.DL_Timer_initPWMMode)
    DL_Timer_initTimerMode                   0x00000b89   Thumb Code   220  dl_timer.o(.text.DL_Timer_initTimerMode)
    DL_Timer_setCaptCompUpdateMethod         0x00000c71   Thumb Code    24  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareOutCtl         0x00000c8d   Thumb Code    20  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x00000ca5   Thumb Code    12  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x00000cb5   Thumb Code    24  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x00000cd1   Thumb Code    64  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x00000d19   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    DL_UART_transmitDataBlocking             0x00000d2d   Thumb Code    32  dl_uart.o(.text.DL_UART_transmitDataBlocking)
    Emm_V5_En_Control                        0x00000d51   Thumb Code    54  emm_v5.o(.text.Emm_V5_En_Control)
    Emm_V5_Origin_Trigger_Return             0x00000d87   Thumb Code    50  emm_v5.o(.text.Emm_V5_Origin_Trigger_Return)
    Emm_V5_Stop_Now                          0x00000db9   Thumb Code    50  emm_v5.o(.text.Emm_V5_Stop_Now)
    Encoder_init                             0x00000ded   Thumb Code    12  enconder.o(.text.Encoder_init)
    Erect_pid                                0x00000dfd   Thumb Code    86  control.o(.text.Erect_pid)
    GPIO_WriteBit                            0x00000e55   Thumb Code    24  led_key.o(.text.GPIO_WriteBit)
    Get_Encoder                              0x00000e71   Thumb Code    20  enconder.o(.text.Get_Encoder)
    IIC_XIE                                  0x00000e8d   Thumb Code   320  oled.o(.text.IIC_XIE)
    IIC_XIE_DATA                             0x00000fcd   Thumb Code   176  oled.o(.text.IIC_XIE_DATA)
    IIC_XIE_ML                               0x0000107d   Thumb Code   176  oled.o(.text.IIC_XIE_ML)
    Line_Control                             0x00001131   Thumb Code   208  control.o(.text.Line_Control)
    MaixCam_Init                             0x00001215   Thumb Code    12  maixcam.o(.text.MaixCam_Init)
    MaixCam_Send_XY                          0x00001229   Thumb Code   172  maixcam.o(.text.MaixCam_Send_XY)
    Motor_Write                              0x000012d9   Thumb Code   140  motor.o(.text.Motor_Write)
    OLED_Clear                               0x00001379   Thumb Code   224  oled.o(.text.OLED_Clear)
    OLED_Init                                0x0000145d   Thumb Code   260  oled.o(.text.OLED_Init)
    OLED_ShowChar                            0x00001565   Thumb Code   244  oled.o(.text.OLED_ShowChar)
    OLED_Write                               0x00001661   Thumb Code   140  oled.o(.text.OLED_Write)
    OLED_task                                0x000016f1   Thumb Code    16  startuptask.o(.text.OLED_task)
    PID_Init                                 0x00001701   Thumb Code    20  control.o(.text.PID_Init)
    POS_Control                              0x00001729   Thumb Code   312  emm_v5.o(.text.POS_Control)
    PendSV_Handler                           0x00001871   Thumb Code    68  port.o(.text.PendSV_Handler)
    QEI_task                                 0x000018b5   Thumb Code    48  startuptask.o(.text.QEI_task)
    RGB_task                                 0x000018e9   Thumb Code    16  startuptask.o(.text.RGB_task)
    SVC_Handler                              0x000018f9   Thumb Code     2  port.o(.text.SVC_Handler)
    SYSCFG_DL_GPIO_init                      0x000018fd   Thumb Code   168  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_MOTOR_PWM_init                 0x000019cd   Thumb Code   120  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_init)
    SYSCFG_DL_QEI_0_init                     0x00001a5d   Thumb Code    44  ti_msp_dl_config.o(.text.SYSCFG_DL_QEI_0_init)
    SYSCFG_DL_SPI_WS2812_init                0x00001aa9   Thumb Code    56  ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_WS2812_init)
    SYSCFG_DL_SYSCTL_init                    0x00001af5   Thumb Code    52  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x00001b31   Thumb Code     2  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_Servo_init                     0x00001b35   Thumb Code   188  ti_msp_dl_config.o(.text.SYSCFG_DL_Servo_init)
    SYSCFG_DL_TIMER_0_init                   0x00001c09   Thumb Code    48  ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_0_init)
    SYSCFG_DL_UART_0_init                    0x00001c51   Thumb Code   104  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_UART_1_init                    0x00001ccd   Thumb Code    88  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    SYSCFG_DL_UART_3_init                    0x00001d35   Thumb Code   100  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    SYSCFG_DL_init                           0x00001db5   Thumb Code    80  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00001e15   Thumb Code    88  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    Set0_task                                0x00001e9d   Thumb Code   252  startuptask.o(.text.Set0_task)
    Speed_Control                            0x00001fa1   Thumb Code   308  emm_v5.o(.text.Speed_Control)
    StartupTask                              0x000020dd   Thumb Code   164  startuptask.o(.text.StartupTask)
    SysTick_Handler                          0x000021d1   Thumb Code    32  port.o(.text.SysTick_Handler)
    TASK_1                                   0x000021f5   Thumb Code   180  control.o(.text.TASK_1)
    TASK_3                                   0x000022bd   Thumb Code   100  control.o(.text.TASK_3)
    TI                                       0x00002331   Thumb Code   792  control.o(.text.TI)
    UART3_IRQHandler                         0x00002695   Thumb Code   124  maixcam.o(.text.UART3_IRQHandler)
    Uart_Send                                0x00002721   Thumb Code    28  bsp_uart.o(.text.Uart_Send)
    circle_task                              0x0000273d   Thumb Code     8  startuptask.o(.text.circle_task)
    delay_noOS                               0x00002745   Thumb Code   120  delay.o(.text.delay_noOS)
    line_task                                0x000027c1   Thumb Code   104  startuptask.o(.text.line_task)
    main                                     0x00002839   Thumb Code    60  main.o(.text.main)
    main_task                                0x00002875   Thumb Code   872  startuptask.o(.text.main_task)
    pvPortMalloc                             0x00003029   Thumb Code   372  heap_4.o(.text.pvPortMalloc)
    pxPortInitialiseStack                    0x000031a1   Thumb Code    32  port.o(.text.pxPortInitialiseStack)
    ulSetInterruptMaskFromISR                0x000031c5   Thumb Code     8  port.o(.text.ulSetInterruptMaskFromISR)
    uxListRemove                             0x000031cd   Thumb Code    34  list.o(.text.uxListRemove)
    vClearInterruptMaskFromISR               0x000031ef   Thumb Code     6  port.o(.text.vClearInterruptMaskFromISR)
    vListInitialise                          0x000031f5   Thumb Code    20  list.o(.text.vListInitialise)
    vListInitialiseItem                      0x00003209   Thumb Code     6  list.o(.text.vListInitialiseItem)
    vListInsert                              0x0000320f   Thumb Code    60  list.o(.text.vListInsert)
    vPortEnterCritical                       0x0000324d   Thumb Code    20  port.o(.text.vPortEnterCritical)
    vPortExitCritical                        0x00003261   Thumb Code    24  port.o(.text.vPortExitCritical)
    vPortFree                                0x0000327d   Thumb Code   148  heap_4.o(.text.vPortFree)
    vPortSetupTimerInterrupt                 0x00003315   Thumb Code    20  port.o(.text.vPortSetupTimerInterrupt)
    vPortYield                               0x00003365   Thumb Code    20  port.o(.text.vPortYield)
    vQueueAddToRegistry                      0x0000337d   Thumb Code   244  queue.o(.text.vQueueAddToRegistry)
    vQueueWaitForMessageRestricted           0x00003475   Thumb Code    92  queue.o(.text.vQueueWaitForMessageRestricted)
    vTaskDelay                               0x000034d1   Thumb Code   116  tasks.o(.text.vTaskDelay)
    vTaskInternalSetTimeOutState             0x00003545   Thumb Code    12  tasks.o(.text.vTaskInternalSetTimeOutState)
    vTaskMissedYield                         0x00003551   Thumb Code     8  tasks.o(.text.vTaskMissedYield)
    vTaskPlaceOnEventList                    0x00003559   Thumb Code   128  tasks.o(.text.vTaskPlaceOnEventList)
    vTaskPlaceOnEventListRestricted          0x000035d9   Thumb Code   160  tasks.o(.text.vTaskPlaceOnEventListRestricted)
    vTaskStartScheduler                      0x00003679   Thumb Code    72  tasks.o(.text.vTaskStartScheduler)
    vTaskSuspendAll                          0x000036d5   Thumb Code    12  tasks.o(.text.vTaskSuspendAll)
    vTaskSwitchContext                       0x000036e1   Thumb Code   136  tasks.o(.text.vTaskSwitchContext)
    xPortStartScheduler                      0x00003775   Thumb Code    84  port.o(.text.xPortStartScheduler)
    xQueueGenericCreate                      0x000037d1   Thumb Code   150  queue.o(.text.xQueueGenericCreate)
    xQueueReceive                            0x00003867   Thumb Code   322  queue.o(.text.xQueueReceive)
    xTaskCheckForTimeOut                     0x000039a9   Thumb Code   116  tasks.o(.text.xTaskCheckForTimeOut)
    xTaskCreate                              0x00003a21   Thumb Code  1068  tasks.o(.text.xTaskCreate)
    xTaskGetSchedulerState                   0x00003e61   Thumb Code    24  tasks.o(.text.xTaskGetSchedulerState)
    xTaskGetTickCount                        0x00003e79   Thumb Code     8  tasks.o(.text.xTaskGetTickCount)
    xTaskIncrementTick                       0x00003e85   Thumb Code   304  tasks.o(.text.xTaskIncrementTick)
    xTaskRemoveFromEventList                 0x00003fb5   Thumb Code   172  tasks.o(.text.xTaskRemoveFromEventList)
    xTaskResumeAll                           0x00004061   Thumb Code   264  tasks.o(.text.xTaskResumeAll)
    xTimerCreateTimerTask                    0x00004175   Thumb Code   108  timers.o(.text.xTimerCreateTimerTask)
    __0vsprintf                              0x000041f5   Thumb Code    30  printfa.o(i.__0vsprintf)
    __1vsprintf                              0x000041f5   Thumb Code     0  printfa.o(i.__0vsprintf)
    __2vsprintf                              0x000041f5   Thumb Code     0  printfa.o(i.__0vsprintf)
    __c89vsprintf                            0x000041f5   Thumb Code     0  printfa.o(i.__0vsprintf)
    vsprintf                                 0x000041f5   Thumb Code     0  printfa.o(i.__0vsprintf)
    __ARM_clz                                0x00004219   Thumb Code    46  depilogue.o(i.__ARM_clz)
    __scatterload_copy                       0x00004249   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x00004259   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x00004261   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    F6x8                                     0x00004b26   Data         552  oled.o(.rodata.F6x8)
    F8X16                                    0x00004d4e   Data        1520  oled.o(.rodata.F8X16)
    uxTopUsedPriority                        0x000053b4   Data           4  tasks.o(.rodata.uxTopUsedPriority)
    Region$$Table$$Base                      0x000053b8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x000053d8   Number         0  anon$$obj.o(Region$$Table)
    Greenx                                   0x20200000   Data           4  startuptask.o(.data..L_MergedGlobals)
    Greeny                                   0x20200004   Data           4  startuptask.o(.data..L_MergedGlobals)
    yutai_3_init_falg                        0x20200008   Data           4  startuptask.o(.data..L_MergedGlobals)
    OLED_Lock                                0x2020000c   Data           4  oled.o(.data.OLED_Lock)
    car_go_dir                               0x20200010   Data           4  startuptask.o(.data.car_go_dir)
    dir                                      0x20200014   Data           2  enconder.o(.data.dir)
    gSPI_WS2812Backup                        0x20200020   Data          40  ti_msp_dl_config.o(.bss..L_MergedGlobals)
    gUART_3Backup                            0x20200048   Data          48  ti_msp_dl_config.o(.bss..L_MergedGlobals)
    Serx                                     0x20200144   Data          44  control.o(.bss..L_MergedGlobals)
    Sery                                     0x20200170   Data          44  control.o(.bss..L_MergedGlobals)
    JiGuang                                  0x2020019e   Data           4  maixcam.o(.bss..L_MergedGlobals)
    Camera_fps                               0x202001a4   Data           4  maixcam.o(.bss..L_MergedGlobals)
    Camera_flag                              0x202001a8   Data           4  maixcam.o(.bss..L_MergedGlobals)
    last_x                                   0x202001f8   Data           2  startuptask.o(.bss..L_MergedGlobals.7)
    last_y                                   0x202001fa   Data           2  startuptask.o(.bss..L_MergedGlobals.7)
    Start_flag                               0x20200218   Data           4  startuptask.o(.bss..L_MergedGlobals.7)
    Set0_flag                                0x2020021c   Data           4  startuptask.o(.bss..L_MergedGlobals.7)
    YunTai_EN                                0x20200220   Data           4  startuptask.o(.bss..L_MergedGlobals.7)
    Line_EN                                  0x20200224   Data           4  startuptask.o(.bss..L_MergedGlobals.7)
    distance                                 0x20200228   Data           4  startuptask.o(.bss..L_MergedGlobals.7)
    yuntai_flag                              0x2020022c   Data           4  startuptask.o(.bss..L_MergedGlobals.7)
    open_line_falg                           0x20200230   Data           4  startuptask.o(.bss..L_MergedGlobals.7)
    location_err_falg                        0x20200234   Data           4  startuptask.o(.bss..L_MergedGlobals.7)
    err_cx                                   0x20200238   Data           4  startuptask.o(.bss..L_MergedGlobals.7)
    err_cy                                   0x2020023c   Data           4  startuptask.o(.bss..L_MergedGlobals.7)
    speed                                    0x20200240   Data           4  startuptask.o(.bss..L_MergedGlobals.7)
    Line                                     0x20200244   Data           4  control.o(.bss.Line)
    a                                        0x20200248   Data           1  control.o(.bss.a)
    gQEI_0Backup                             0x2020024c   Data         120  ti_msp_dl_config.o(.bss.gQEI_0Backup)
    gServoBackup                             0x202002c4   Data         188  ti_msp_dl_config.o(.bss.gServoBackup)
    gTIMER_0Backup                           0x20200380   Data         120  ti_msp_dl_config.o(.bss.gTIMER_0Backup)
    pxCurrentTCB                             0x202003f8   Data           4  tasks.o(.bss.pxCurrentTCB)
    xQueueRegistry                           0x2020585c   Data          64  queue.o(.bss.xQueueRegistry)
    __initial_sp                             0x202060a0   Data           0  startup_mspm0g350x_uvision.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x000053f8, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x000053d8, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO          842    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000000   Code   RO         1486  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x000000c0   0x000000c0   0x00000004   Code   RO         1553    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x000000c4   0x000000c4   0x00000004   Code   RO         1556    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO         1558    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO         1560    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x000000c8   0x000000c8   0x00000008   Code   RO         1561    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO         1563    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO         1565    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x000000d0   0x000000d0   0x00000004   Code   RO         1554    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x000000d4   0x000000d4   0x00000014   Code   RO          843    .text               startup_mspm0g350x_uvision.o
    0x000000e8   0x000000e8   0x00000030   Code   RO         1495    .text               mc_p.l(llmul.o)
    0x00000118   0x00000118   0x00000024   Code   RO         1497    .text               mc_p.l(memcpya.o)
    0x0000013c   0x0000013c   0x00000024   Code   RO         1499    .text               mc_p.l(memseta.o)
    0x00000160   0x00000160   0x000000b2   Code   RO         1531    .text               mf_p.l(fadd.o)
    0x00000212   0x00000212   0x0000007a   Code   RO         1533    .text               mf_p.l(fmul.o)
    0x0000028c   0x0000028c   0x0000001c   Code   RO         1537    .text               mf_p.l(fcmple.o)
    0x000002a8   0x000002a8   0x0000001c   Code   RO         1539    .text               mf_p.l(fcmplt.o)
    0x000002c4   0x000002c4   0x0000001c   Code   RO         1541    .text               mf_p.l(fcmpge.o)
    0x000002e0   0x000002e0   0x0000001c   Code   RO         1543    .text               mf_p.l(fcmpgt.o)
    0x000002fc   0x000002fc   0x00000016   Code   RO         1545    .text               mf_p.l(fflti.o)
    0x00000312   0x00000312   0x0000000e   Code   RO         1547    .text               mf_p.l(ffltui.o)
    0x00000320   0x00000320   0x00000032   Code   RO         1549    .text               mf_p.l(ffixi.o)
    0x00000352   0x00000352   0x00000028   Code   RO         1551    .text               mf_p.l(ffixui.o)
    0x0000037a   0x0000037a   0x0000003e   Code   RO         1572    .text               mc_p.l(uidiv_div0.o)
    0x000003b8   0x000003b8   0x00000060   Code   RO         1578    .text               mc_p.l(uldiv.o)
    0x00000418   0x00000418   0x00000000   Code   RO         1580    .text               mc_p.l(iusefp.o)
    0x00000418   0x00000418   0x00000082   Code   RO         1581    .text               mf_p.l(fepilogue.o)
    0x0000049a   0x0000049a   0x00000002   PAD
    0x0000049c   0x0000049c   0x00000164   Code   RO         1583    .text               mf_p.l(dadd.o)
    0x00000600   0x00000600   0x000000d0   Code   RO         1585    .text               mf_p.l(dmul.o)
    0x000006d0   0x000006d0   0x000000f0   Code   RO         1587    .text               mf_p.l(ddiv.o)
    0x000007c0   0x000007c0   0x00000040   Code   RO         1589    .text               mf_p.l(dfixul.o)
    0x00000800   0x00000800   0x00000028   Code   RO         1591    .text               mf_p.l(cdrcmple.o)
    0x00000828   0x00000828   0x00000024   Code   RO         1593    .text               mc_p.l(init.o)
    0x0000084c   0x0000084c   0x00000020   Code   RO         1595    .text               mc_p.l(llshl.o)
    0x0000086c   0x0000086c   0x00000022   Code   RO         1597    .text               mc_p.l(llushr.o)
    0x0000088e   0x0000088e   0x00000026   Code   RO         1599    .text               mc_p.l(llsshr.o)
    0x000008b4   0x000008b4   0x000000be   Code   RO         1601    .text               mf_p.l(depilogue.o)
    0x00000972   0x00000972   0x0000000a   Code   RO           51    .text.DL_Common_delayCycles  dl_common.o
    0x0000097c   0x0000097c   0x00000044   Code   RO          511    .text.DL_SPI_init   dl_spi.o
    0x000009c0   0x000009c0   0x00000012   Code   RO          513    .text.DL_SPI_setClockConfig  dl_spi.o
    0x000009d2   0x000009d2   0x00000002   PAD
    0x000009d4   0x000009d4   0x000000f4   Code   RO          643    .text.DL_TimerA_initPWMMode  dl_timer.o
    0x00000ac8   0x00000ac8   0x000000c0   Code   RO          587    .text.DL_Timer_initPWMMode  dl_timer.o
    0x00000b88   0x00000b88   0x000000e8   Code   RO          569    .text.DL_Timer_initTimerMode  dl_timer.o
    0x00000c70   0x00000c70   0x0000001c   Code   RO          617    .text.DL_Timer_setCaptCompUpdateMethod  dl_timer.o
    0x00000c8c   0x00000c8c   0x00000018   Code   RO          591    .text.DL_Timer_setCaptureCompareOutCtl  dl_timer.o
    0x00000ca4   0x00000ca4   0x00000010   Code   RO          571    .text.DL_Timer_setCaptureCompareValue  dl_timer.o
    0x00000cb4   0x00000cb4   0x0000001c   Code   RO          565    .text.DL_Timer_setClockConfig  dl_timer.o
    0x00000cd0   0x00000cd0   0x00000048   Code   RO          676    .text.DL_UART_init  dl_uart.o
    0x00000d18   0x00000d18   0x00000012   Code   RO          678    .text.DL_UART_setClockConfig  dl_uart.o
    0x00000d2a   0x00000d2a   0x00000002   PAD
    0x00000d2c   0x00000d2c   0x00000024   Code   RO          690    .text.DL_UART_transmitDataBlocking  dl_uart.o
    0x00000d50   0x00000d50   0x00000036   Code   RO         1450    .text.Emm_V5_En_Control  emm_v5.o
    0x00000d86   0x00000d86   0x00000032   Code   RO         1460    .text.Emm_V5_Origin_Trigger_Return  emm_v5.o
    0x00000db8   0x00000db8   0x00000032   Code   RO         1452    .text.Emm_V5_Stop_Now  emm_v5.o
    0x00000dea   0x00000dea   0x00000002   PAD
    0x00000dec   0x00000dec   0x00000010   Code   RO         1334    .text.Encoder_init  enconder.o
    0x00000dfc   0x00000dfc   0x00000056   Code   RO         1362    .text.Erect_pid     control.o
    0x00000e52   0x00000e52   0x00000002   PAD
    0x00000e54   0x00000e54   0x0000001c   Code   RO         1323    .text.GPIO_WriteBit  led_key.o
    0x00000e70   0x00000e70   0x0000001c   Code   RO         1332    .text.Get_Encoder   enconder.o
    0x00000e8c   0x00000e8c   0x00000140   Code   RO         1274    .text.IIC_XIE       oled.o
    0x00000fcc   0x00000fcc   0x000000b0   Code   RO         1278    .text.IIC_XIE_DATA  oled.o
    0x0000107c   0x0000107c   0x000000b4   Code   RO         1276    .text.IIC_XIE_ML    oled.o
    0x00001130   0x00001130   0x000000e4   Code   RO         1366    .text.Line_Control  control.o
    0x00001214   0x00001214   0x00000014   Code   RO         1399    .text.MaixCam_Init  maixcam.o
    0x00001228   0x00001228   0x000000b0   Code   RO         1401    .text.MaixCam_Send_XY  maixcam.o
    0x000012d8   0x000012d8   0x000000a0   Code   RO         1313    .text.Motor_Write   motor.o
    0x00001378   0x00001378   0x000000e4   Code   RO         1282    .text.OLED_Clear    oled.o
    0x0000145c   0x0000145c   0x00000108   Code   RO         1294    .text.OLED_Init     oled.o
    0x00001564   0x00001564   0x000000fc   Code   RO         1284    .text.OLED_ShowChar  oled.o
    0x00001660   0x00001660   0x00000090   Code   RO         1296    .text.OLED_Write    oled.o
    0x000016f0   0x000016f0   0x00000010   Code   RO         1198    .text.OLED_task     startuptask.o
    0x00001700   0x00001700   0x00000028   Code   RO         1356    .text.PID_Init      control.o
    0x00001728   0x00001728   0x00000140   Code   RO         1436    .text.POS_Control   emm_v5.o
    0x00001868   0x00001868   0x00000008   PAD
    0x00001870   0x00001870   0x00000044   Code   RO         1181    .text.PendSV_Handler  port.o
    0x000018b4   0x000018b4   0x00000034   Code   RO         1208    .text.QEI_task      startuptask.o
    0x000018e8   0x000018e8   0x00000010   Code   RO         1196    .text.RGB_task      startuptask.o
    0x000018f8   0x000018f8   0x00000002   Code   RO         1161    .text.SVC_Handler   port.o
    0x000018fa   0x000018fa   0x00000002   PAD
    0x000018fc   0x000018fc   0x000000d0   Code   RO          788    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x000019cc   0x000019cc   0x00000090   Code   RO          792    .text.SYSCFG_DL_MOTOR_PWM_init  ti_msp_dl_config.o
    0x00001a5c   0x00001a5c   0x0000004c   Code   RO          796    .text.SYSCFG_DL_QEI_0_init  ti_msp_dl_config.o
    0x00001aa8   0x00001aa8   0x0000004c   Code   RO          806    .text.SYSCFG_DL_SPI_WS2812_init  ti_msp_dl_config.o
    0x00001af4   0x00001af4   0x0000003c   Code   RO          790    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00001b30   0x00001b30   0x00000002   Code   RO          808    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x00001b32   0x00001b32   0x00000002   PAD
    0x00001b34   0x00001b34   0x000000d4   Code   RO          794    .text.SYSCFG_DL_Servo_init  ti_msp_dl_config.o
    0x00001c08   0x00001c08   0x00000048   Code   RO          798    .text.SYSCFG_DL_TIMER_0_init  ti_msp_dl_config.o
    0x00001c50   0x00001c50   0x0000007c   Code   RO          800    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x00001ccc   0x00001ccc   0x00000068   Code   RO          802    .text.SYSCFG_DL_UART_1_init  ti_msp_dl_config.o
    0x00001d34   0x00001d34   0x00000080   Code   RO          804    .text.SYSCFG_DL_UART_3_init  ti_msp_dl_config.o
    0x00001db4   0x00001db4   0x00000060   Code   RO          784    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00001e14   0x00001e14   0x00000088   Code   RO          786    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00001e9c   0x00001e9c   0x00000104   Code   RO         1204    .text.Set0_task     startuptask.o
    0x00001fa0   0x00001fa0   0x0000013c   Code   RO         1432    .text.Speed_Control  emm_v5.o
    0x000020dc   0x000020dc   0x000000f4   Code   RO         1194    .text.StartupTask   startuptask.o
    0x000021d0   0x000021d0   0x00000024   Code   RO         1183    .text.SysTick_Handler  port.o
    0x000021f4   0x000021f4   0x000000c8   Code   RO         1370    .text.TASK_1        control.o
    0x000022bc   0x000022bc   0x00000074   Code   RO         1372    .text.TASK_3        control.o
    0x00002330   0x00002330   0x00000364   Code   RO         1376    .text.TI            control.o
    0x00002694   0x00002694   0x0000008c   Code   RO         1403    .text.UART3_IRQHandler  maixcam.o
    0x00002720   0x00002720   0x0000001c   Code   RO         1478    .text.Uart_Send     bsp_uart.o
    0x0000273c   0x0000273c   0x00000008   Code   RO         1206    .text.circle_task   startuptask.o
    0x00002744   0x00002744   0x0000007c   Code   RO         1346    .text.delay_noOS    delay.o
    0x000027c0   0x000027c0   0x00000078   Code   RO         1202    .text.line_task     startuptask.o
    0x00002838   0x00002838   0x0000003c   Code   RO          775    .text.main          main.o
    0x00002874   0x00002874   0x000003b4   Code   RO         1200    .text.main_task     startuptask.o
    0x00002c28   0x00002c28   0x0000004c   Code   RO         1002    .text.prvIdleTask   tasks.o
    0x00002c74   0x00002c74   0x00000034   Code   RO         1159    .text.prvTaskExitError  port.o
    0x00002ca8   0x00002ca8   0x00000300   Code   RO         1089    .text.prvTimerTask  timers.o
    0x00002fa8   0x00002fa8   0x0000007e   Code   RO          923    .text.prvUnlockQueue  queue.o
    0x00003026   0x00003026   0x00000002   PAD
    0x00003028   0x00003028   0x00000178   Code   RO         1133    .text.pvPortMalloc  heap_4.o
    0x000031a0   0x000031a0   0x00000024   Code   RO         1157    .text.pxPortInitialiseStack  port.o
    0x000031c4   0x000031c4   0x00000008   Code   RO         1177    .text.ulSetInterruptMaskFromISR  port.o
    0x000031cc   0x000031cc   0x00000022   Code   RO          893    .text.uxListRemove  list.o
    0x000031ee   0x000031ee   0x00000006   Code   RO         1179    .text.vClearInterruptMaskFromISR  port.o
    0x000031f4   0x000031f4   0x00000014   Code   RO          885    .text.vListInitialise  list.o
    0x00003208   0x00003208   0x00000006   Code   RO          887    .text.vListInitialiseItem  list.o
    0x0000320e   0x0000320e   0x0000003c   Code   RO          891    .text.vListInsert   list.o
    0x0000324a   0x0000324a   0x00000002   PAD
    0x0000324c   0x0000324c   0x00000014   Code   RO         1173    .text.vPortEnterCritical  port.o
    0x00003260   0x00003260   0x0000001c   Code   RO         1175    .text.vPortExitCritical  port.o
    0x0000327c   0x0000327c   0x00000098   Code   RO         1135    .text.vPortFree     heap_4.o
    0x00003314   0x00003314   0x0000001c   Code   RO         1165    .text.vPortSetupTimerInterrupt  port.o
    0x00003330   0x00003330   0x00000034   Code   RO         1167    .text.vPortStartFirstTask  port.o
    0x00003364   0x00003364   0x00000018   Code   RO         1171    .text.vPortYield    port.o
    0x0000337c   0x0000337c   0x000000f8   Code   RO          957    .text.vQueueAddToRegistry  queue.o
    0x00003474   0x00003474   0x0000005c   Code   RO          961    .text.vQueueWaitForMessageRestricted  queue.o
    0x000034d0   0x000034d0   0x00000074   Code   RO          982    .text.vTaskDelay    tasks.o
    0x00003544   0x00003544   0x0000000c   Code   RO         1032    .text.vTaskInternalSetTimeOutState  tasks.o
    0x00003550   0x00003550   0x00000008   Code   RO         1036    .text.vTaskMissedYield  tasks.o
    0x00003558   0x00003558   0x00000080   Code   RO         1020    .text.vTaskPlaceOnEventList  tasks.o
    0x000035d8   0x000035d8   0x000000a0   Code   RO         1024    .text.vTaskPlaceOnEventListRestricted  tasks.o
    0x00003678   0x00003678   0x0000005c   Code   RO         1000    .text.vTaskStartScheduler  tasks.o
    0x000036d4   0x000036d4   0x0000000c   Code   RO          978    .text.vTaskSuspendAll  tasks.o
    0x000036e0   0x000036e0   0x00000094   Code   RO          994    .text.vTaskSwitchContext  tasks.o
    0x00003774   0x00003774   0x0000005c   Code   RO         1163    .text.xPortStartScheduler  port.o
    0x000037d0   0x000037d0   0x00000096   Code   RO          905    .text.xQueueGenericCreate  queue.o
    0x00003866   0x00003866   0x00000142   Code   RO          929    .text.xQueueReceive  queue.o
    0x000039a8   0x000039a8   0x00000078   Code   RO         1034    .text.xTaskCheckForTimeOut  tasks.o
    0x00003a20   0x00003a20   0x00000440   Code   RO          972    .text.xTaskCreate   tasks.o
    0x00003e60   0x00003e60   0x00000018   Code   RO         1048    .text.xTaskGetSchedulerState  tasks.o
    0x00003e78   0x00003e78   0x0000000c   Code   RO         1008    .text.xTaskGetTickCount  tasks.o
    0x00003e84   0x00003e84   0x00000130   Code   RO         1006    .text.xTaskIncrementTick  tasks.o
    0x00003fb4   0x00003fb4   0x000000ac   Code   RO         1026    .text.xTaskRemoveFromEventList  tasks.o
    0x00004060   0x00004060   0x00000114   Code   RO          980    .text.xTaskResumeAll  tasks.o
    0x00004174   0x00004174   0x00000080   Code   RO         1087    .text.xTimerCreateTimerTask  timers.o
    0x000041f4   0x000041f4   0x00000024   Code   RO         1510    i.__0vsprintf       mc_p.l(printfa.o)
    0x00004218   0x00004218   0x0000002e   Code   RO         1603    i.__ARM_clz         mf_p.l(depilogue.o)
    0x00004246   0x00004246   0x00000002   PAD
    0x00004248   0x00004248   0x0000000e   Code   RO         1607    i.__scatterload_copy  mc_p.l(handlers.o)
    0x00004256   0x00004256   0x00000002   PAD
    0x00004258   0x00004258   0x00000002   Code   RO         1608    i.__scatterload_null  mc_p.l(handlers.o)
    0x0000425a   0x0000425a   0x00000006   PAD
    0x00004260   0x00004260   0x0000000e   Code   RO         1609    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x0000426e   0x0000426e   0x00000002   PAD
    0x00004270   0x00004270   0x00000174   Code   RO         1511    i._fp_digits        mc_p.l(printfa.o)
    0x000043e4   0x000043e4   0x000006ec   Code   RO         1512    i._printf_core      mc_p.l(printfa.o)
    0x00004ad0   0x00004ad0   0x00000020   Code   RO         1513    i._printf_post_padding  mc_p.l(printfa.o)
    0x00004af0   0x00004af0   0x0000002c   Code   RO         1514    i._printf_pre_padding  mc_p.l(printfa.o)
    0x00004b1c   0x00004b1c   0x0000000a   Code   RO         1516    i._sputc            mc_p.l(printfa.o)
    0x00004b26   0x00004b26   0x00000228   Data   RO         1298    .rodata.F6x8        oled.o
    0x00004d4e   0x00004d4e   0x000005f0   Data   RO         1299    .rodata.F8X16       oled.o
    0x0000533e   0x0000533e   0x00000003   Data   RO          817    .rodata.gMOTOR_PWMClockConfig  ti_msp_dl_config.o
    0x00005341   0x00005341   0x00000003   PAD
    0x00005344   0x00005344   0x00000008   Data   RO          818    .rodata.gMOTOR_PWMConfig  ti_msp_dl_config.o
    0x0000534c   0x0000534c   0x00000003   Data   RO          821    .rodata.gQEI_0ClockConfig  ti_msp_dl_config.o
    0x0000534f   0x0000534f   0x00000002   Data   RO          830    .rodata.gSPI_WS2812_clockConfig  ti_msp_dl_config.o
    0x00005351   0x00005351   0x00000001   PAD
    0x00005352   0x00005352   0x0000000a   Data   RO          831    .rodata.gSPI_WS2812_config  ti_msp_dl_config.o
    0x0000535c   0x0000535c   0x00000003   Data   RO          819    .rodata.gServoClockConfig  ti_msp_dl_config.o
    0x0000535f   0x0000535f   0x00000001   PAD
    0x00005360   0x00005360   0x00000008   Data   RO          820    .rodata.gServoConfig  ti_msp_dl_config.o
    0x00005368   0x00005368   0x00000003   Data   RO          822    .rodata.gTIMER_0ClockConfig  ti_msp_dl_config.o
    0x0000536b   0x0000536b   0x00000001   PAD
    0x0000536c   0x0000536c   0x00000014   Data   RO          823    .rodata.gTIMER_0TimerConfig  ti_msp_dl_config.o
    0x00005380   0x00005380   0x00000002   Data   RO          824    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x00005382   0x00005382   0x0000000a   Data   RO          825    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x0000538c   0x0000538c   0x00000002   Data   RO          826    .rodata.gUART_1ClockConfig  ti_msp_dl_config.o
    0x0000538e   0x0000538e   0x0000000a   Data   RO          827    .rodata.gUART_1Config  ti_msp_dl_config.o
    0x00005398   0x00005398   0x00000002   Data   RO          828    .rodata.gUART_3ClockConfig  ti_msp_dl_config.o
    0x0000539a   0x0000539a   0x0000000a   Data   RO          829    .rodata.gUART_3Config  ti_msp_dl_config.o
    0x000053a4   0x000053a4   0x00000005   Data   RO         1123    .rodata.str1.1      timers.o
    0x000053a9   0x000053a9   0x00000009   Data   RO         1381    .rodata.str1.1      control.o
    0x000053b2   0x000053b2   0x00000002   PAD
    0x000053b4   0x000053b4   0x00000004   Data   RO         1075    .rodata.uxTopUsedPriority  tasks.o
    0x000053b8   0x000053b8   0x00000020   Data   RO         1606    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x000053d8, Size: 0x000060a0, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x000053d8   0x0000000c   Data   RW         1226    .data..L_MergedGlobals  startuptask.o
    0x2020000c   0x000053e4   0x00000004   Data   RW         1302    .data.OLED_Lock     oled.o
    0x20200010   0x000053e8   0x00000004   Data   RW         1222    .data.car_go_dir    startuptask.o
    0x20200014   0x000053ec   0x00000002   Data   RW         1336    .data.dir           enconder.o
    0x20200016   0x000053ee   0x00000002   PAD
    0x20200018   0x000053f0   0x00000004   Data   RW         1185    .data.uxCriticalNesting  port.o
    0x2020001c   0x000053f4   0x00000004   PAD
    0x20200020        -       0x00000058   Zero   RW          832    .bss..L_MergedGlobals  ti_msp_dl_config.o
    0x20200078        -       0x00000074   Zero   RW         1077    .bss..L_MergedGlobals  tasks.o
    0x202000ec        -       0x0000003c   Zero   RW         1124    .bss..L_MergedGlobals  timers.o
    0x20200128        -       0x0000001c   Zero   RW         1148    .bss..L_MergedGlobals  heap_4.o
    0x20200144        -       0x00000058   Zero   RW         1382    .bss..L_MergedGlobals  control.o
    0x2020019c        -       0x00000034   Zero   RW         1405    .bss..L_MergedGlobals  maixcam.o
    0x202001d0        -       0x00000028   Zero   RW         1078    .bss..L_MergedGlobals.1  tasks.o
    0x202001f8        -       0x0000004c   Zero   RW         1227    .bss..L_MergedGlobals.7  startuptask.o
    0x20200244        -       0x00000004   Zero   RW         1378    .bss.Line           control.o
    0x20200248        -       0x00000001   Zero   RW         1379    .bss.a              control.o
    0x20200249   0x000053f4   0x00000003   PAD
    0x2020024c        -       0x00000078   Zero   RW          815    .bss.gQEI_0Backup   ti_msp_dl_config.o
    0x202002c4        -       0x000000bc   Zero   RW          814    .bss.gServoBackup   ti_msp_dl_config.o
    0x20200380        -       0x00000078   Zero   RW          816    .bss.gTIMER_0Backup  ti_msp_dl_config.o
    0x202003f8        -       0x00000004   Zero   RW         1074    .bss.pxCurrentTCB   tasks.o
    0x202003fc        -       0x00000460   Zero   RW         1076    .bss.pxReadyTasksLists  tasks.o
    0x2020085c        -       0x00005000   Zero   RW         1147    .bss.ucHeap         heap_4.o
    0x2020585c        -       0x00000040   Zero   RW          963    .bss.xQueueRegistry  queue.o
    0x2020589c   0x000053f4   0x00000004   PAD
    0x202058a0        -       0x00000800   Zero   RW          840    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        28          0          0          0          0       2596   bsp_uart.o
      1538        156          9          0         93       8924   control.o
       124          4          0          0          0       1350   delay.o
        10          0          0          0          0        621   dl_common.o
        86          8          0          0          0      15615   dl_spi.o
       764        188          0          0          0      37624   dl_timer.o
       126         12          0          0          0      14426   dl_uart.o
       790         16          0          0          0      11382   emm_v5.o
        44         12          0          2          0       3707   enconder.o
       528          8          0          0      20508       5146   heap_4.o
        28          4          0          0          0       3389   led_key.o
       120          0          0          0          0       2172   list.o
        60          0          0          0          0       2436   main.o
       336         28          0          0         52       8780   maixcam.o
       160         20          0          0          0       6972   motor.o
      1564         24       2072          4          0      11980   oled.o
       452         44          0          4          0       2794   port.o
       938          4          0          0         64      22930   queue.o
        20          4        192          0       2048        732   startup_mspm0g350x_uvision.o
      1664        184          0         16         76       6481   startuptask.o
      2748         80          4          0       1280      40576   tasks.o
      1438        300         96          0        516      38938   ti_msp_dl_config.o
       896         34          5          0         60      14235   timers.o

    ----------------------------------------------------------------------
     14486       <USER>       <GROUP>         28      24708     263806   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        24          8          8          2         11          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        48          0          0          0          0         72   llmul.o
        32          0          0          0          0         68   llshl.o
        38          0          0          0          0         68   llsshr.o
        34          0          0          0          0         68   llushr.o
        36          0          0          0          0         60   memcpya.o
        36          0          0          0          0        100   memseta.o
      2266         96          0          0          0        460   printfa.o
        62          0          0          0          0         72   uidiv_div0.o
        96          0          0          0          0         84   uldiv.o
        40          2          0          0          0         68   cdrcmple.o
       356          4          0          0          0        140   dadd.o
       240          6          0          0          0         84   ddiv.o
       236          0          0          0          0        216   depilogue.o
        64         10          0          0          0         68   dfixul.o
       208          6          0          0          0         88   dmul.o
       178          0          0          0          0        108   fadd.o
        28          0          0          0          0         60   fcmpge.o
        28          0          0          0          0         60   fcmpgt.o
        28          0          0          0          0         60   fcmple.o
        28          0          0          0          0         60   fcmplt.o
       130          0          0          0          0        144   fepilogue.o
        50          0          0          0          0         60   ffixi.o
        40          0          0          0          0         60   ffixui.o
        22          0          0          0          0         68   fflti.o
        14          0          0          0          0         68   ffltui.o
       122          0          0          0          0         72   fmul.o

    ----------------------------------------------------------------------
      4560        <USER>          <GROUP>          0          0       2604   Library Totals
        14          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2734        112          0          0          0       1120   mc_p.l
      1812         28          0          0          0       1484   mf_p.l

    ----------------------------------------------------------------------
      4560        <USER>          <GROUP>          0          0       2604   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     19046       1278       2418         28      24708     264814   Grand Totals
     19046       1278       2418         28      24708     264814   ELF Image Totals
     19046       1278       2418         28          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                21464 (  20.96kB)
    Total RW  Size (RW Data + ZI Data)             24736 (  24.16kB)
    Total ROM Size (Code + RO Data + RW Data)      21492 (  20.99kB)

==============================================================================

