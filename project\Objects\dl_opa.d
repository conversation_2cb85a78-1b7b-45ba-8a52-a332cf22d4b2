./objects/dl_opa.o: ..\lib\ti\driverlib\dl_opa.c \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_opa.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\stdbool.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\stdint.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\msp.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\DeviceFamily.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\m0p\mspm0g350x.h \
  ..\lib\ti\CMSIS\core_cm0plus.h ..\lib\ti\CMSIS\cmsis_version.h \
  ..\lib\ti\CMSIS\cmsis_compiler.h ..\lib\ti\CMSIS\cmsis_armclang.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\arm_compat.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\arm_acle.h \
  ..\lib\ti\CMSIS\mpu_armv7.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_adc12.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_aes.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_comp.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_crc.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_dac12.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_dma.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_flashctl.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_gpio.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_gptimer.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_i2c.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_iomux.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_mathacl.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_mcan.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_oa.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_rtc.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_spi.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_trng.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_uart.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_vref.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_wuc.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_wwdt.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\m0p\hw_debugss.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_common.h
