./objects/uart.o: ..\Hardware\UART\UART.c ..\user\main.h \
  ..\user\ti_msp_dl_config.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\msp.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\DeviceFamily.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\m0p\mspm0g350x.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\stdint.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\stdbool.h \
  ..\lib\ti\CMSIS\core_cm0plus.h ..\lib\ti\CMSIS\cmsis_version.h \
  ..\lib\ti\CMSIS\cmsis_compiler.h ..\lib\ti\CMSIS\cmsis_armclang.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\arm_compat.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\arm_acle.h \
  ..\lib\ti\CMSIS\mpu_armv7.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_adc12.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_aes.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_comp.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_crc.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_dac12.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_dma.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_flashctl.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_gpio.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_gptimer.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_i2c.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_iomux.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_mathacl.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_mcan.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_oa.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_rtc.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_spi.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_trng.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_uart.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_vref.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_wuc.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\hw_wwdt.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\m0p\hw_factoryregion.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\m0p\hw_cpuss.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\m0p\hw_debugss.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\m0p\hw_sysctl.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\driverlib.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_adc12.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_common.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_aes.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\stddef.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_aesadv.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_comp.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_crc.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_crcp.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_dac12.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_dma.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_flashctl.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\m0p\dl_factoryregion.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\m0p\dl_core.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\m0p\dl_sysctl.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_gpamp.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_gpio.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_i2c.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_iwdt.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_lfss.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_keystorectl.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_lcd.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_mathacl.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_mcan.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_opa.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_rtc.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_rtc_common.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_rtc_a.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_scratchpad.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_spi.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_tamperio.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_timera.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_timer.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_timerg.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_trng.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_uart_extend.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_uart.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_uart_main.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_vref.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\dl_wwdt.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\m0p\dl_interrupt.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\ti\driverlib\m0p\dl_systick.h \
  ..\FreeRTOS\include\FreeRTOS.h ..\FreeRTOS\include\FreeRTOSConfig.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\stdio.h \
  ..\FreeRTOS\include\projdefs.h ..\FreeRTOS\include\portable.h \
  ..\FreeRTOS\include\deprecated_definitions.h \
  ..\FreeRTOS\portable\ARM_CM0\portmacro.h \
  ..\FreeRTOS\include\mpu_wrappers.h ..\FreeRTOS\include\task.h \
  ..\FreeRTOS\include\list.h ..\FreeRTOS\include\queue.h \
  ..\Task\StartupTask.h ..\Hardware\delay\delay.h \
  ..\Hardware\LED_Key\LED_Key.h ..\Hardware\IMU\IMU.h \
  ..\Hardware\UART\UART.h ..\Hardware\Emm_V5\Emm_V5.h \
  ..\Hardware\Timer\Timer.h ..\Hardware\Enconder\Enconder.h \
  ..\Hardware\Motor\Motor.h ..\Hardware\oled\oled.h \
  ..\Hardware\servo\Servo.h ..\Hardware\ws2812\ws2812b.h \
  ..\Hardware\control\control.h ..\Hardware\MaixCam\MaixCam.h \
  ..\Hardware\YunQue\YunQue.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\stdarg.h \
  H:\app\keil5\ARM\ARMCLANG\Bin\..\include\string.h
