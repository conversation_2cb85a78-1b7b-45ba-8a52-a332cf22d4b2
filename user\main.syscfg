/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3505" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.1+4189"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const GPIO4   = GPIO.addInstance();
const GPIO5   = GPIO.addInstance();
const GPIO6   = GPIO.addInstance();
const GPIO7   = GPIO.addInstance();
const GPIO8   = GPIO.addInstance();
const GPIO9   = GPIO.addInstance();
const GPIO10  = GPIO.addInstance();
const GPIO11  = GPIO.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const PWM2    = PWM.addInstance();
const QEI     = scripting.addModule("/ti/driverlib/QEI", {}, false);
const QEI1    = QEI.addInstance();
const SPI     = scripting.addModule("/ti/driverlib/SPI", {}, false);
const SPI1    = SPI.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER   = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1  = TIMER.addInstance();
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();
const UART2   = UART.addInstance();
const UART3   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

GPIO1.$name                          = "BUZZER";
GPIO1.port                           = "PORTA";
GPIO1.associatedPins[0].$name        = "PIN_0";
GPIO1.associatedPins[0].assignedPin  = "7";
GPIO1.associatedPins[0].initialValue = "SET";
GPIO1.associatedPins[0].pin.$assign  = "PA7";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.port                           = "PORTA";
GPIO2.portSegment                    = "Upper";
GPIO2.$name                          = "OLED";
GPIO2.associatedPins.create(2);
GPIO2.associatedPins[0].initialValue = "SET";
GPIO2.associatedPins[0].assignedPin  = "31";
GPIO2.associatedPins[0].$name        = "SCL";
GPIO2.associatedPins[0].ioStructure  = "HD";
GPIO2.associatedPins[1].initialValue = "SET";
GPIO2.associatedPins[1].assignedPin  = "28";
GPIO2.associatedPins[1].ioStructure  = "HD";
GPIO2.associatedPins[1].$name        = "SDA";

GPIO3.$name                         = "M2_1_Dir";
GPIO3.port                          = "PORTA";
GPIO3.associatedPins[0].assignedPin = "16";
GPIO3.associatedPins[0].$name       = "PIN_3";
GPIO3.associatedPins[0].pin.$assign = "PA16";

GPIO4.port                          = "PORTB";
GPIO4.$name                         = "M1_2_Dir";
GPIO4.associatedPins[0].$name       = "PIN_2";
GPIO4.associatedPins[0].assignedPin = "17";
GPIO4.associatedPins[0].pin.$assign = "PB17";

GPIO5.port                          = "PORTB";
GPIO5.$name                         = "M1_1_Dir";
GPIO5.associatedPins[0].assignedPin = "19";
GPIO5.associatedPins[0].$name       = "PIN_1";
GPIO5.associatedPins[0].pin.$assign = "PB19";

GPIO6.port                          = "PORTB";
GPIO6.$name                         = "M2_2_Dir";
GPIO6.associatedPins[0].assignedPin = "24";
GPIO6.associatedPins[0].$name       = "PIN_4";

GPIO7.$name                         = "RED";
GPIO7.port                          = "PORTA";
GPIO7.associatedPins[0].$name       = "LED1";
GPIO7.associatedPins[0].assignedPin = "2";

GPIO8.$name                         = "BLUE";
GPIO8.port                          = "PORTA";
GPIO8.associatedPins[0].$name       = "LED3";
GPIO8.associatedPins[0].assignedPin = "18";
GPIO8.associatedPins[0].pin.$assign = "PA18";

GPIO9.$name                         = "IOA";
GPIO9.port                          = "PORTA";
GPIO9.associatedPins.create(3);
GPIO9.associatedPins[0].$name       = "IOA1";
GPIO9.associatedPins[0].assignedPin = "10";
GPIO9.associatedPins[1].$name       = "IOA2";
GPIO9.associatedPins[1].assignedPin = "11";
GPIO9.associatedPins[2].$name       = "IOA3";
GPIO9.associatedPins[2].assignedPin = "30";

GPIO10.$name                              = "HW";
GPIO10.port                               = "PORTB";
GPIO10.portSegment                        = "Lower";
GPIO10.associatedPins.create(4);
GPIO10.associatedPins[0].direction        = "INPUT";
GPIO10.associatedPins[0].$name            = "HW1";
GPIO10.associatedPins[0].assignedPin      = "0";
GPIO10.associatedPins[0].internalResistor = "PULL_UP";
GPIO10.associatedPins[0].pin.$assign      = "PB0";
GPIO10.associatedPins[1].$name            = "HW2";
GPIO10.associatedPins[1].direction        = "INPUT";
GPIO10.associatedPins[1].assignedPin      = "1";
GPIO10.associatedPins[1].internalResistor = "PULL_UP";
GPIO10.associatedPins[2].$name            = "HW3";
GPIO10.associatedPins[2].direction        = "INPUT";
GPIO10.associatedPins[2].assignedPin      = "10";
GPIO10.associatedPins[2].internalResistor = "PULL_UP";
GPIO10.associatedPins[3].$name            = "HW4";
GPIO10.associatedPins[3].direction        = "INPUT";
GPIO10.associatedPins[3].assignedPin      = "11";
GPIO10.associatedPins[3].internalResistor = "PULL_UP";

GPIO11.port                               = "PORTB";
GPIO11.portSegment                        = "Upper";
GPIO11.$name                              = "KEY1";
GPIO11.associatedPins.create(2);
GPIO11.associatedPins[0].direction        = "INPUT";
GPIO11.associatedPins[0].$name            = "PIN_21";
GPIO11.associatedPins[0].assignedPin      = "25";
GPIO11.associatedPins[0].internalResistor = "PULL_UP";
GPIO11.associatedPins[0].inputFilter      = "3_CYCLES";
GPIO11.associatedPins[1].$name            = "PIN_22";
GPIO11.associatedPins[1].direction        = "INPUT";
GPIO11.associatedPins[1].assignedPin      = "22";
GPIO11.associatedPins[1].inputFilter      = "3_CYCLES";
GPIO11.associatedPins[1].internalResistor = "PULL_UP";

PWM1.$name                              = "MOTOR_PWM";
PWM1.timerCount                         = 10000;
PWM1.timerStartTimer                    = true;
PWM1.peripheral.$assign                 = "TIMG0";
PWM1.peripheral.ccp0Pin.$assign         = "PA12";
PWM1.peripheral.ccp1Pin.$assign         = "PA13";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_0.invert               = true;
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.PWM_CHANNEL_1.invert               = true;
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric6";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric7";

PWM2.$name                              = "Servo";
PWM2.pwmMode                            = "EDGE_ALIGN_UP";
PWM2.timerStartTimer                    = true;
PWM2.ccIndex                            = [0,1,2,3];
PWM2.clockPrescale                      = 2;
PWM2.clockDivider                       = 5;
PWM2.timerCount                         = 64000;
PWM2.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM2.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC3";
PWM2.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric8";
PWM2.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric10";
PWM2.peripheral.$assign                 = "TIMA0";
PWM2.peripheral.ccp0Pin.$assign         = "PA21";
PWM2.peripheral.ccp1Pin.$assign         = "PA22";
PWM2.peripheral.ccp2Pin.$assign         = "PA15";
PWM2.peripheral.ccp3Pin.$assign         = "PA17";
PWM2.PWM_CHANNEL_2.$name                = "ti_driverlib_pwm_PWMTimerCC4";
PWM2.PWM_CHANNEL_3.$name                = "ti_driverlib_pwm_PWMTimerCC5";
PWM2.ccp2PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric11";
PWM2.ccp3PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric14";

QEI1.$name                      = "QEI_0";
QEI1.peripheral.ccp0Pin.$assign = "PA26";
QEI1.peripheral.ccp1Pin.$assign = "PA27";
QEI1.ccp0PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric15";
QEI1.ccp1PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric16";

SPI1.$name                                    = "SPI_WS2812";
SPI1.frameFormat                              = "MOTO3";
SPI1.sclkPinConfig.direction                  = scripting.forceWrite("OUTPUT");
SPI1.sclkPinConfig.onlyInternalResistor       = scripting.forceWrite(false);
SPI1.sclkPinConfig.passedPeripheralType       = scripting.forceWrite("Digital");
SPI1.sclkPinConfig.$name                      = "ti_driverlib_gpio_GPIOPinGeneric4";
SPI1.mosiPinConfig.direction                  = scripting.forceWrite("OUTPUT");
SPI1.mosiPinConfig.hideOutputInversion        = scripting.forceWrite(false);
SPI1.mosiPinConfig.onlyInternalResistor       = scripting.forceWrite(false);
SPI1.mosiPinConfig.passedPeripheralType       = scripting.forceWrite("Digital");
SPI1.mosiPinConfig.$name                      = "ti_driverlib_gpio_GPIOPinGeneric5";
SPI1.misoPinConfig.onlyInternalResistor       = scripting.forceWrite(false);
SPI1.misoPinConfig.passedPeripheralType       = scripting.forceWrite("Digital");
SPI1.misoPinConfig.$name                      = "ti_driverlib_gpio_GPIOPinGeneric9";
SPI1.peripheral.$assign                       = "SPI1";
SPI1.peripheral.mosiPin.$assign               = "PB15";
SPI1.peripheral.misoPin.$assignAllowConflicts = "PB14";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

SYSTICK.period = 32000;

TIMER1.$name              = "TIMER_0";
TIMER1.timerStartTimer    = true;
TIMER1.timerClkDiv        = 8;
TIMER1.timerClkPrescale   = 40;
TIMER1.timerMode          = "PERIODIC";
TIMER1.interrupts         = ["ZERO"];
TIMER1.interruptPriority  = "0";
TIMER1.timerPeriod        = "10ms";
TIMER1.peripheral.$assign = "TIMG7";

UART1.$name                    = "UART_0";
UART1.enabledInterrupts        = ["RX"];
UART1.targetBaudRate           = 115200;
UART1.uartClkDiv               = "2";
UART1.interruptPriority        = "3";
UART1.peripheral.$assign       = "UART0";
UART1.peripheral.rxPin.$assign = "PA1";
UART1.peripheral.txPin.$assign = "PA0";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric12";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric13";

UART2.$name                    = "UART_1";
UART2.targetBaudRate           = 115200;
UART2.enabledInterrupts        = ["RX"];
UART2.peripheral.rxPin.$assign = "PB7";
UART2.peripheral.txPin.$assign = "PB6";
UART2.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART2.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

UART3.$name                            = "UART_3";
UART3.targetBaudRate                   = 115200;
UART3.enabledInterrupts                = ["RX","TX"];
UART3.interruptPriority                = "0";
UART3.peripheral.$assign               = "UART3";
UART3.peripheral.rxPin.$assign         = "PB3";
UART3.peripheral.txPin.$assign         = "PB2";
UART3.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART3.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART3.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART3.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART3.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
UART3.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART3.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART3.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART3.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution             = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution    = "PA20";
Board.peripheral.swdioPin.$suggestSolution    = "PA19";
GPIO2.associatedPins[0].pin.$suggestSolution  = "PA31";
GPIO2.associatedPins[1].pin.$suggestSolution  = "PA28";
GPIO6.associatedPins[0].pin.$suggestSolution  = "PB24";
GPIO7.associatedPins[0].pin.$suggestSolution  = "PA2";
GPIO9.associatedPins[0].pin.$suggestSolution  = "PA10";
GPIO9.associatedPins[1].pin.$suggestSolution  = "PA11";
GPIO9.associatedPins[2].pin.$suggestSolution  = "PA30";
GPIO10.associatedPins[1].pin.$suggestSolution = "PB1";
GPIO10.associatedPins[2].pin.$suggestSolution = "PB10";
GPIO10.associatedPins[3].pin.$suggestSolution = "PB11";
GPIO11.associatedPins[0].pin.$suggestSolution = "PB25";
GPIO11.associatedPins[1].pin.$suggestSolution = "PB22";
QEI1.peripheral.$suggestSolution              = "TIMG8";
SPI1.peripheral.sclkPin.$suggestSolution      = "PB16";
UART2.peripheral.$suggestSolution             = "UART1";
